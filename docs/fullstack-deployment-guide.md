# 全栈应用部署流程指南

## 🏗️ 架构概览

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   用户请求      │    │   Cloudflare     │    │   AWS 基础设施   │
│                 │    │                  │    │                 │
│ app.domain.com  │───▶│ • DNS 解析       │───▶│ • CloudFront CDN │
│                 │    │ • SSL 证书       │    │ • S3 静态资源    │
│                 │    │ • WAF 防护       │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API 请求      │    │   Cloudflare     │    │   AWS Fargate   │
│                 │    │                  │    │                 │
│ api.domain.com  │───▶│ • DNS 解析       │───▶│ • Docker 容器    │
│                 │    │ • SSL 证书       │    │ • Load Balancer  │
│                 │    │ • 代理转发       │    │ • Auto Scaling   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 📁 项目结构

建议的项目结构：

```
your-fullstack-app/
├── infrastructure/           # 基础设施代码
│   └── a1d-pulumi/           # 我们当前的基础设施
├── backend/                 # 后端应用
│   ├── src/
│   ├── Dockerfile
│   └── package.json
├── frontend/                # 前端应用
│   ├── src/
│   ├── dist/               # 构建输出
│   └── package.json
├── shared/                  # 共享类型/工具
└── deployment/              # 部署脚本
    ├── deploy-frontend.sh
    ├── deploy-backend.sh
    └── deploy-full.sh
```

## 🚀 部署流程概述

### 1. 基础设施部署 (一次性)
### 2. 后端应用部署 (Docker + Fargate/Container Apps)
### 3. 前端应用部署 (S3 + CloudFront/Storage + CDN)

## ⚠️ Azure Container Apps 部署经验总结

基于 `examples/azure-backend-app` 的实战经验，以下是关键的踩坑指南：

### 🔑 关键配置要点

#### 1. 容器网络配置（最重要！）
```typescript
// 在 Bun.serve 中必须监听 0.0.0.0
Bun.serve({
    port: 3000,
    hostname: "0.0.0.0", // 关键！容器外部访问必需
    // ...
});
```

#### 2. Docker 镜像推送
```bash
# ❌ 避免 docker push（认证复杂）
docker push registry.azurecr.io/app:latest

# ✅ 使用 ACR 云端构建
az acr build --registry myregistry --image app:latest .
```

#### 3. Dockerfile 兼容性
- 移除 `--platform=linux/arm64` 参数
- Bun 镜像基于 Debian，使用 `apt-get` 而非 `apk`
- 用户创建使用 `groupadd`/`useradd` 而非 Alpine 语法

#### 4. Pulumi Stack 引用
```yaml
# 正确的 Stack Reference 格式
config:
  app:infraStackRef: "organization/project/stack"
```

### 📋 部署前检查清单

- [ ] Azure CLI 已登录
- [ ] 基础设施已部署
- [ ] 应用监听 `0.0.0.0`
- [ ] Dockerfile 使用正确语法
- [ ] Stack 引用路径正确

详细信息请参考：`examples/azure-backend-app/DEPLOYMENT.md`
### 4. 域名配置 (Cloudflare)
### 5. CI/CD 自动化

---

## 🔧 1. 基础设施部署

### 前置条件

- Pulumi CLI
- AWS CLI
- jq (JSON 处理工具)
- Docker (后端部署)

### 部署步骤

1. 配置 AWS 凭证
2. 设置 Pulumi 配置
3. 部署基础设施
4. 导出配置文件

## 🐳 2. 后端应用部署

### Dockerfile 配置

使用多阶段构建优化镜像大小：

```dockerfile
# 构建阶段
FROM node:20-alpine AS builder
WORKDIR /app
COPY package*.json pnpm-lock.yaml ./
RUN npm install -g pnpm && pnpm install --frozen-lockfile
COPY . .
RUN pnpm run build

# 生产阶段
FROM node:20-alpine AS production
WORKDIR /app
RUN addgroup -g 1001 -S nodejs && adduser -S nextjs -u 1001
COPY package*.json pnpm-lock.yaml ./
RUN npm install -g pnpm && pnpm install --frozen-lockfile --prod
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist
USER nextjs
EXPOSE 3000
CMD ["node", "dist/index.js"]
```

### 部署流程

1. **构建镜像**: 在本地或 CI/CD 中构建 Docker 镜像
2. **推送到 ECR**: 上传到 AWS 容器仓库
3. **部署到 Fargate**: 更新 ECS 服务配置
4. **健康检查**: 验证服务正常运行

## 🎨 3. 前端应用部署

### Vite 构建配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
        },
      },
    },
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
  },
});
```

### 环境变量配置

```bash
# .env.production
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_CDN_URL=https://d1234567890.cloudfront.net
VITE_APP_ENV=production
```

### 部署流程

1. **安装依赖**: pnpm install
2. **构建应用**: pnpm run build
3. **上传静态资源**: S3 sync (长缓存)
4. **上传页面文件**: S3 sync (短缓存)
5. **刷新 CDN**: CloudFront 缓存失效

### 缓存策略

| 文件类型 | 缓存时间 | Cache-Control |
|---------|----------|---------------|
| HTML 文件 | 1小时 | `public, max-age=3600` |
| CSS/JS 文件 | 1年 | `public, max-age=31536000, immutable` |
| 图片文件 | 1年 | `public, max-age=31536000, immutable` |

## 🌐 4. Cloudflare 域名配置

### DNS 记录

| 类型 | 名称 | 目标 | 代理状态 |
|------|------|------|----------|
| CNAME | app | d1234567890.cloudfront.net | 🟠 已代理 |
| CNAME | api | alb-name-123456789.ap-southeast-2.elb.amazonaws.com | 🟠 已代理 |

### 页面规则

#### 静态资源缓存
- **URL**: `app.yourdomain.com/assets/*`
- **设置**:
  - Cache Level: Cache Everything
  - Edge Cache TTL: 1 month
  - Browser Cache TTL: 1 month

#### API 请求
- **URL**: `api.yourdomain.com/*`
- **设置**:
  - Cache Level: Bypass
  - Security Level: Medium
  - SSL: Full (Strict)

### SSL/TLS 配置

- **加密模式**: Full (Strict)
- **最小 TLS 版本**: 1.2
- **自动 HTTPS 重写**: 开启
- **HSTS**: 启用

## 🔄 5. CI/CD 自动化

### GitHub Actions 工作流结构

```yaml
# .github/workflows/deploy.yml
name: Deploy Full Stack App
on:
  push:
    branches: [main]

jobs:
  deploy-infrastructure:  # 部署基础设施
  deploy-backend:        # 构建和部署后端
  deploy-frontend:       # 构建和部署前端
```

### 环境变量和密钥

需要在 GitHub Secrets 中配置：

- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`
- `PULUMI_ACCESS_TOKEN`

## 📊 监控和维护

### CloudWatch 监控指标

- **Fargate 服务**: CPU、内存使用率
- **CloudFront**: 请求数、缓存命中率
- **RDS**: 连接数、查询性能
- **Redis**: 内存使用、连接数

### 日志管理

- **应用日志**: CloudWatch Logs
- **访问日志**: S3 存储
- **错误追踪**: 集成第三方服务

## 🔧 实用工具脚本

项目包含以下实用脚本：

- `scripts/generate-env.ts` - 导出 Pulumi 配置和环境变量
- `scripts/deploy-frontend-example.ts` - 前端部署示例
- `scripts/create-project-structure.ts` - 创建项目结构
- `scripts/cost-calculator.ts` - 成本计算器

## 📋 部署检查清单

### 首次部署
- [ ] AWS 凭证配置完成
- [ ] Pulumi 项目初始化
- [ ] 数据库密码设置
- [ ] 基础设施部署成功
- [ ] 配置文件导出完成

### 后端部署
- [ ] Dockerfile 配置正确
- [ ] 依赖安装完成
- [ ] 镜像构建成功
- [ ] ECR 推送完成
- [ ] Fargate 服务运行

### 前端部署
- [ ] 环境变量配置
- [ ] 构建产物生成
- [ ] S3 上传完成
- [ ] CloudFront 缓存刷新
- [ ] 网站访问正常

### 域名配置
- [ ] DNS 记录添加
- [ ] SSL 证书激活
- [ ] 页面规则配置
- [ ] 缓存策略设置

## 🚨 故障排除

### 常见问题

1. **Pulumi 部署失败**
   - 检查 AWS 权限
   - 验证配置参数
   - 查看详细错误日志

2. **Docker 构建失败**
   - 检查 Dockerfile 语法
   - 验证依赖版本
   - 清理 Docker 缓存

3. **CloudFront 缓存问题**
   - 手动创建失效请求
   - 检查缓存策略配置
   - 验证 S3 权限设置

4. **域名解析问题**
   - 检查 DNS 记录
   - 验证 Cloudflare 代理状态
   - 测试 SSL 证书

### 性能优化建议

1. **前端优化**
   - 启用代码分割
   - 优化图片格式
   - 使用 CDN 预加载

2. **后端优化**
   - 配置数据库连接池
   - 实现 Redis 缓存
   - 启用 API 响应压缩

3. **基础设施优化**
   - 调整 Fargate 资源配置
   - 优化 CloudFront 缓存策略
   - 配置自动扩缩容

---

## 📚 相关文档

- [S3 + CloudFront 使用指南](./aws/s3-cloudfront-guide.md)
- [Fargate 应用部署指南](../examples/fargate-app/fargate-deployment-guide.md)
- [本地开发环境设置](./aws/aws-local-development-tunnels.md)

这个部署流程将为你提供一个生产就绪的全栈应用架构，结合了 AWS 的可扩展性和 Cloudflare 的全球 CDN 性能优势。🚀