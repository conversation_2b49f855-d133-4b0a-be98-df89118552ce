# AWS vs Azure 服务对比

本文档详细对比了项目中使用的 AWS 和 Azure 服务，帮助你选择合适的云平台。

## 🏗️ 架构对比

### AWS 架构

```mermaid
graph TB
    subgraph "AWS 架构"
        subgraph VPC["VPC (10.0.0.0/16)"]
            subgraph PubSubnet["公有子网"]
                PubSubnet1["公有子网 1<br/>(********/24)"]
                PubSubnet2["公有子网 2<br/>(********/24)"]
                Fargate["Fargate 服务"]
            end

            subgraph PrivSubnet["私有子网"]
                PrivSubnet1["私有子网 1<br/>(********/24)"]
                PrivSubnet2["私有子网 2<br/>(********/24)"]
                RDS["RDS PostgreSQL"]
                ElastiCache["ElastiCache Redis"]
            end

            Bastion["Bastion Host"]
        end

        S3["S3 Bucket"]
        CloudFront["CloudFront CDN"]
        ECR["ECR 容器注册表"]

        PubSubnet1 -.-> Fargate
        PubSubnet2 -.-> Fargate
        PrivSubnet1 -.-> RDS
        PrivSubnet1 -.-> ElastiCache
        PrivSubnet2 -.-> RDS
        PrivSubnet2 -.-> ElastiCache

        Fargate --> RDS
        Fargate --> ElastiCache
        Fargate --> S3
        CloudFront --> S3
        ECR --> Fargate
        Bastion --> PrivSubnet1
        Bastion --> PrivSubnet2
    end
```

### Azure 架构

```mermaid
graph TB
    subgraph "Azure 架构"
        subgraph VNet["Virtual Network (10.0.0.0/16)"]
            subgraph PubSubnet["公有子网"]
                PubSubnet1["公有子网<br/>(********/24)"]
                ContainerApps["Container Apps"]
            end

            subgraph PrivSubnet["私有子网"]
                PrivSubnet1["私有子网<br/>(********/24)"]
                PostgreSQL["Database for PostgreSQL"]
                Redis["Cache for Redis"]
            end

            AzureBastion["Azure Bastion"]
        end

        BlobStorage["Blob Storage"]
        AzureCDN["Azure CDN"]
        ContainerRegistry["Container Registry"]

        PubSubnet1 -.-> ContainerApps
        PrivSubnet1 -.-> PostgreSQL
        PrivSubnet1 -.-> Redis

        ContainerApps --> PostgreSQL
        ContainerApps --> Redis
        ContainerApps --> BlobStorage
        AzureCDN --> BlobStorage
        ContainerRegistry --> ContainerApps
        AzureBastion --> PrivSubnet1
    end
```

## 📊 服务对比表

| 功能 | AWS 服务 | Azure 服务 | 对比说明 |
|------|----------|-------------|----------|
| **计算** | | | |
| 容器服务 | Fargate | Container Apps | Azure 更简单，AWS 更灵活 |
| 容器注册表 | ECR | Container Registry | 功能相似，Azure 集成更好 |
| **存储** | | | |
| 对象存储 | S3 | Blob Storage | S3 功能更丰富，Blob Storage 更便宜 |
| CDN | CloudFront | Azure CDN | CloudFront 全球节点更多 |
| **数据库** | | | |
| PostgreSQL | RDS | Database for PostgreSQL | RDS 功能更丰富，Azure 更简单 |
| Redis | ElastiCache | Cache for Redis | 功能相似，价格相近 |
| **网络** | | | |
| 虚拟网络 | VPC | Virtual Network | 功能相似，AWS 更成熟 |
| 安全组 | Security Groups | Network Security Groups | 功能相似 |
| 堡垒机 | Bastion Host | Azure Bastion | Azure Bastion 更简单 |
| **监控** | | | |
| 监控服务 | CloudWatch | Azure Monitor | 功能相似，Azure 界面更友好 |
| 日志服务 | CloudWatch Logs | Log Analytics | 功能相似 |

## 💰 成本对比

### 开发环境 (月费用估算)

| 服务类型 | AWS | Azure | 差异 |
|----------|-----|-------|------|
| 数据库 | $20 (db.t4g.micro) | $15 (Standard_B1ms) | Azure 便宜 25% |
| 缓存 | $15 (cache.t4g.micro) | $15 (Basic C0) | 相似 |
| 存储 | $5 (S3 + CloudFront) | $10 (Blob + CDN) | AWS 便宜 50% |
| 容器 | $10 (Fargate) | $10 (Container Apps) | 相似 |
| 网络 | $5 (VPC + NAT) | $5 (VNet + NAT) | 相似 |
| **总计** | **$55/月** | **$55/月** | **基本相同** |

### 生产环境 (月费用估算)

| 服务类型 | AWS | Azure | 差异 |
|----------|-----|-------|------|
| 数据库 | $150 (db.r6g.large) | $120 (Standard_D2s_v3) | Azure 便宜 20% |
| 缓存 | $80 (cache.r6g.large) | $75 (Standard C1) | Azure 便宜 6% |
| 存储 | $50 (S3 + CloudFront) | $70 (Blob + CDN) | AWS 便宜 29% |
| 容器 | $200 (Fargate) | $180 (Container Apps) | Azure 便宜 10% |
| 网络 | $20 (VPC + NAT) | $20 (VNet + NAT) | 相似 |
| **总计** | **$500/月** | **$465/月** | **Azure 便宜 7%** |

## ⚡ 性能对比

### 数据库性能

| 指标 | AWS RDS | Azure Database |
|------|---------|----------------|
| CPU 架构 | ARM64 (Graviton) | ARM64 |
| 存储类型 | gp3 SSD | Premium SSD |
| IOPS | 3000-16000 | 500-7500 |
| 备份保留 | 35 天 | 35 天 |
| 高可用 | Multi-AZ | Zone Redundant |

### 网络延迟 (从中国大陆)

| 区域 | AWS | Azure | 推荐 |
|------|-----|-------|------|
| 香港 | ap-southeast-1 (30ms) | East Asia (25ms) | Azure |
| 新加坡 | ap-southeast-1 (50ms) | Southeast Asia (45ms) | Azure |
| 东京 | ap-northeast-1 (80ms) | Japan East (75ms) | Azure |

## 🔧 开发体验对比

### 部署复杂度

| 方面 | AWS | Azure | 说明 |
|------|-----|-------|------|
| 认证设置 | ⭐⭐⭐ | ⭐⭐⭐⭐ | Azure CLI 更简单 |
| 资源配置 | ⭐⭐⭐ | ⭐⭐⭐⭐ | Azure 资源组概念更清晰 |
| 网络配置 | ⭐⭐ | ⭐⭐⭐ | AWS 更复杂但更灵活 |
| 监控设置 | ⭐⭐⭐ | ⭐⭐⭐⭐ | Azure Monitor 界面更友好 |

### 文档和社区

| 方面 | AWS | Azure | 说明 |
|------|-----|-------|------|
| 官方文档 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | AWS 文档更全面 |
| 社区支持 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | AWS 社区更活跃 |
| 中文资源 | ⭐⭐⭐ | ⭐⭐⭐⭐ | Azure 中文资源更多 |
| 学习曲线 | ⭐⭐ | ⭐⭐⭐⭐ | Azure 更容易上手 |

## 🎯 选择建议

### 选择 AWS 的情况

✅ **适合以下场景**：
- 需要最丰富的服务生态
- 对性能和可扩展性要求极高
- 团队有 AWS 经验
- 需要全球多区域部署
- 预算充足

❌ **不适合以下场景**：
- 团队缺乏云计算经验
- 预算有限
- 需要快速上手

### 选择 Azure 的情况

✅ **适合以下场景**：
- 团队使用微软技术栈
- 需要快速上手云计算
- 预算有限
- 主要服务中国大陆用户
- 重视开发体验

❌ **不适合以下场景**：
- 需要最新的云服务功能
- 对 AWS 生态有强依赖
- 需要复杂的网络配置

## 🔄 迁移指南

### 从 AWS 迁移到 Azure

1. **数据迁移**
   ```bash
   # 数据库迁移
   pg_dump aws_database | psql azure_database

   # 存储迁移
   azcopy copy "s3://bucket/*" "https://storage.blob.core.windows.net/container/"
   ```

2. **配置迁移**
   ```bash
   # 切换分支
   git checkout azure-environment

   # 重新配置
   pulumi config set azure-native:location "East Asia"
   pulumi config set --secret dbPassword "same-password"
   ```

3. **应用配置**
   ```bash
   # 生成新的环境变量
   pnpm run azure:env:gen

   # 更新应用配置
   cp .env.local .env.aws.backup
   cp .env.azure .env.local
   ```

### 从 Azure 迁移到 AWS

1. **数据迁移**
   ```bash
   # 数据库迁移
   pg_dump azure_database | psql aws_database

   # 存储迁移
   aws s3 sync "https://storage.blob.core.windows.net/container/" "s3://bucket/"
   ```

2. **配置迁移**
   ```bash
   # 切换分支
   git checkout main

   # 重新配置
   pulumi config set aws:region ap-southeast-2
   pulumi config set --secret dbPassword "same-password"
   ```

## 📈 未来发展趋势

### AWS 优势趋势
- 持续的服务创新
- 更多的 AI/ML 服务
- 全球基础设施扩张

### Azure 优势趋势
- 与微软生态深度集成
- 混合云解决方案
- 企业级安全和合规

## 🎉 总结

| 场景 | 推荐平台 | 理由 |
|------|----------|------|
| 初学者 | Azure | 更简单，文档友好 |
| 企业级 | AWS | 功能更丰富，生态更成熟 |
| 成本敏感 | Azure | 整体成本略低 |
| 中国用户 | Azure | 延迟更低，本地化更好 |
| 微软技术栈 | Azure | 生态集成更好 |
| 复杂架构 | AWS | 更灵活，选择更多 |

**建议**：如果你是云计算新手或主要服务中国用户，推荐从 Azure 开始。如果你需要最丰富的功能或已有 AWS 经验，选择 AWS。
