# 项目结构说明

本项目已重新组织为多云基础设施管理的最佳实践结构。

## 📁 项目结构

```
a1d-infra/
├── 📁 projects/                    # 各云平台的独立项目
│   ├── 📁 aws/                     # AWS 基础设施项目
│   │   ├── 📄 Pulumi.yaml          # AWS 主配置
│   │   ├── 📄 Pulumi.minimal.yaml  # AWS 测试配置
│   │   ├── 📄 package.json         # AWS 依赖和脚本
│   │   ├── 📄 tsconfig.json        # TypeScript 配置
│   │   ├── 📁 src/                 # AWS 源代码
│   │   │   ├── 📄 index.ts         # AWS 完整基础设施
│   │   │   ├── 📄 index-minimal.ts # AWS 最小测试
│   │   │   └── 📁 lib/             # 共享工具库
│   │   └── 📁 scripts/             # AWS 专用脚本
│   │       ├── 📄 generate-env.ts  # AWS 环境变量生成
│   │       ├── 📄 dev-tunnel.ts    # 开发隧道工具
│   │       └── 📄 utils.ts         # 工具函数
│   └── 📁 azure/                   # Azure 基础设施项目
│       ├── 📄 Pulumi.yaml          # Azure 主配置
│       ├── 📄 Pulumi.minimal.yaml  # Azure 测试配置
│       ├── 📄 package.json         # Azure 依赖和脚本
│       ├── 📄 tsconfig.json        # TypeScript 配置
│       ├── 📁 src/                 # Azure 源代码
│       │   ├── 📄 index.ts         # Azure 完整基础设施
│       │   ├── 📄 index-minimal.ts # Azure 最小测试
│       │   └── 📁 lib/             # 共享工具库
│       └── 📁 scripts/             # Azure 专用脚本
│           ├── 📄 generate-env.ts  # Azure 环境变量生成
│           └── 📄 utils.ts         # 工具函数

├── 📁 docs/                        # 文档
├── 📄 package.json                 # 根项目配置 (workspace)
├── 📄 pnpm-workspace.yaml          # pnpm workspace 配置
├── 📄 Pulumi.yaml                  # 通用 Pulumi 配置
└── 📄 tsconfig.json                # 根 TypeScript 配置
```

## 🎯 设计原则

### 1. **完全隔离**
- 每个云平台有独立的项目目录
- 独立的依赖管理 (`package.json`)
- 独立的配置文件 (`Pulumi.yaml`)
- 独立的源代码目录

### 2. **pnpm Workspace**
- 使用 `pnpm-workspace.yaml` 管理多项目
- 支持过滤执行：`pnpm --filter <project-name> <command>`
- 共享根级别的开发依赖

### 3. **参数化操作**
- 通过项目名称过滤操作特定云平台
- 统一的命令接口，自动路由到正确的项目

## 🚀 使用方法

### 根级别命令（推荐）

```bash
# AWS 操作
pnpm run aws:preview        # 预览 AWS 部署
pnpm run aws:up             # 部署 AWS 基础设施
pnpm run aws:destroy        # 销毁 AWS 资源
pnpm run aws:test           # AWS 快速测试
pnpm run aws:env:gen        # 生成 AWS 环境变量

# Azure 操作
pnpm run azure:preview      # 预览 Azure 部署
pnpm run azure:up           # 部署 Azure 基础设施
pnpm run azure:destroy      # 销毁 Azure 资源
pnpm run azure:test         # Azure 快速测试
pnpm run azure:env:gen      # 生成 Azure 环境变量

# 通用操作
pnpm run aws:tunnel         # 启动 AWS 开发隧道
```

### 项目级别命令

```bash
# 在 AWS 项目中工作
cd projects/aws
pnpm preview               # 预览部署
pnpm up                    # 部署基础设施
pnpm test:preview          # 测试配置预览
pnpm env:gen               # 生成环境变量
pnpm dev:tunnel            # 启动开发隧道

# 在 Azure 项目中工作
cd projects/azure
pnpm preview               # 预览部署
pnpm up                    # 部署基础设施
pnpm test:preview          # 测试配置预览
pnpm env:gen               # 生成环境变量
```

### 直接使用 pnpm workspace

```bash
# 使用过滤器运行特定项目的命令
pnpm --filter a1d-infra-aws preview
pnpm --filter a1d-infra-azure up

# 为特定项目安装依赖
pnpm --filter a1d-infra-aws add @pulumi/eks
pnpm --filter a1d-infra-azure add @pulumi/kubernetes
```

## 🔧 配置管理

### Stack 管理

每个项目维护独立的 stack：

```bash
# AWS Stack 管理
cd projects/aws
pulumi stack init aws-dev
pulumi config set projectName "myapp-aws"
pulumi config set --secret dbPassword "aws-password"

# Azure Stack 管理
cd projects/azure
pulumi stack init azure-dev
pulumi config set projectName "myapp-azure"
pulumi config set --secret dbPassword "azure-password"
```

### 环境变量

每个项目生成独立的环境配置：

```bash
# 生成 .env.local (AWS)
pnpm run aws:env:gen

# 生成 .env.azure (Azure)
pnpm run azure:env:gen
```

## 📦 依赖管理

### 项目特定依赖

```json
// projects/aws/package.json
{
  "dependencies": {
    "@pulumi/aws": "^6.0.0",
    "@pulumi/awsx": "^2.0.2"
  }
}

// projects/azure/package.json
{
  "dependencies": {
    "@pulumi/azure-native": "^3.5.1",
    "@pulumi/azuread": "^6.5.1"
  }
}
```

### 共享依赖

根级别的 `package.json` 包含：
- 开发工具 (TypeScript, 类型定义)
- 公共脚本依赖
- 通用 Pulumi 包

## 🎉 优势

✅ **完全隔离**：每个云平台独立，互不影响
✅ **代码复用**：共享的 lib 和 scripts
✅ **统一接口**：从根目录统一管理所有云平台
✅ **灵活部署**：可以独立操作任一云平台
✅ **清晰结构**：项目结构一目了然
✅ **易于扩展**：添加新云平台只需创建新项目目录

现在你可以完全独立地管理 AWS 和 Azure 基础设施，同时保持代码组织的清晰性！