# Pulumi 多云工作流指南

本指南详细说明如何使用 pnpm workspace 在 AWS 和 Azure 之间进行多云基础设施管理。

## 🏗️ 项目结构

项目采用 pnpm workspace 组织，支持完全独立的多云配置：

```
📁 a1d-infra/
├── 📄 package.json              # 根项目配置和统一脚本
├── 📄 pnpm-workspace.yaml       # pnpm workspace 配置
├── 📁 projects/
│   ├── 📁 aws/                   # AWS 独立项目
│   │   ├── 📄 package.json       # AWS 项目依赖
│   │   ├── 📄 Pulumi.yaml        # AWS 完整基础设施配置
│   │   ├── 📄 Pulumi.minimal.yaml # AWS 最小测试配置
│   │   ├── 📄 tsconfig.json      # AWS TypeScript 配置
│   │   ├── 📁 src/
│   │   │   ├── 📄 index.ts       # AWS 完整基础设施
│   │   │   ├── 📄 index-minimal.ts # AWS 最小测试
│   │   │   └── 📁 lib/           # AWS 共享库
│   │   └── 📁 scripts/           # AWS 脚本工具
│   └── 📁 azure/                 # Azure 独立项目
│       ├── 📄 package.json       # Azure 项目依赖
│       ├── 📄 Pulumi.yaml        # Azure 完整基础设施配置
│       ├── 📄 Pulumi.minimal.yaml # Azure 最小测试配置
│       ├── 📄 tsconfig.json      # Azure TypeScript 配置
│       ├── 📁 src/
│       │   ├── 📄 index.ts       # Azure 完整基础设施
│       │   ├── 📄 index-minimal.ts # Azure 最小测试
│       │   └── 📁 lib/           # Azure 共享库
│       └── 📁 scripts/           # Azure 脚本工具
└── 📁 docs/                     # 文档目录
    ├── 📄 aws-auth-setup.md     # AWS 认证配置
    └── 📁 azure/                # Azure 文档
        ├── 📄 azure-auth-setup.md
        └── 📄 azure-quickstart.md
```

## 🚀 快速开始

### 安装依赖

```bash
# 安装所有项目依赖
pnpm install

# 或单独安装特定项目
pnpm --filter a1d-infra-aws install
pnpm --filter a1d-infra-azure install
```

### AWS 环境操作

```bash
# 从根目录运行 AWS 命令
pnpm run aws:up              # 部署 AWS 完整基础设施
pnpm run aws:preview         # 预览 AWS 部署
pnpm run aws:destroy         # 销毁 AWS 资源
pnpm run aws:test            # AWS 快速测试（仅 S3）
pnpm run aws:env:gen         # 生成 AWS 环境变量
pnpm run aws:tunnel          # 开启开发隧道
```

### Azure 环境操作

```bash
# 从根目录运行 Azure 命令
pnpm run azure:up            # 部署 Azure 完整基础设施
pnpm run azure:preview       # 预览 Azure 部署
pnpm run azure:destroy       # 销毁 Azure 资源
pnpm run azure:test          # Azure 快速测试（仅存储账户）
pnpm run azure:env:gen       # 生成 Azure 环境变量
```

### 直接在子项目中操作

```bash
# 进入 AWS 项目目录
cd projects/aws

# 直接运行 pulumi 命令
pnpm run up                  # 等同于 pulumi up
pnpm run preview             # 等同于 pulumi preview
pnpm run test:up             # 等同于 pulumi up -f Pulumi.minimal.yaml
pnpm run env:gen             # 生成环境变量

# 进入 Azure 项目目录
cd projects/azure

# 直接运行 pulumi 命令
pnpm run up                  # 等同于 pulumi up
pnpm run preview             # 等同于 pulumi preview
pnpm run test:up             # 等同于 pulumi up -f Pulumi.minimal.yaml
pnpm run env:gen             # 生成环境变量
```

## 🔧 高级使用

### Stack 管理

每个子项目都有独立的 stack 管理：

```bash
# AWS Stack 管理
cd projects/aws
pnpm run stack init aws-dev
pnpm run stack select aws-dev
pnpm run config set projectName "myapp-aws"
pnpm run config set --secret dbPassword "aws-password"

# Azure Stack 管理
cd projects/azure
pnpm run stack init azure-dev
pnpm run stack select azure-dev
pnpm run config set projectName "myapp-azure"
pnpm run config set --secret dbPassword "azure-password"
```

### 配置管理

```bash
# AWS 配置
cd projects/aws
pnpm run config set aws:region "ap-southeast-2"
pnpm run config set projectName "a1d-aws"
pnpm run config set --secret dbPassword "secure-password"

# Azure 配置
cd projects/azure
pnpm run config set azure-native:location "East Asia"
pnpm run config set projectName "a1d-azure"
pnpm run config set --secret dbPassword "secure-password"
```

### 测试配置

每个项目都有最小测试配置，用于快速验证：

```bash
# AWS 测试 (只创建 S3 存储桶)
cd projects/aws
pnpm run test:preview        # 预览测试部署
pnpm run test:up             # 部署测试资源
pnpm run test:destroy        # 销毁测试资源

# Azure 测试 (只创建存储账户)
cd projects/azure
pnpm run test:preview        # 预览测试部署
pnpm run test:up             # 部署测试资源
pnpm run test:destroy        # 销毁测试资源
```

## 🌍 多云部署策略

### 1. 从根目录统一管理

```bash
# 同时预览两个平台
pnpm run aws:preview &
pnpm run azure:preview &
wait

# 依次部署
pnpm run aws:up
pnpm run azure:up
```

### 2. 测试优先策略

```bash
# 1. 测试连接性
pnpm run aws:test && pnpm run azure:test

# 2. 确认后部署完整基础设施
pnpm run aws:up && pnpm run azure:up

# 3. 生成环境配置
pnpm run aws:env:gen && pnpm run azure:env:gen
```

## 📝 环境配置生成

### AWS 环境变量

```bash
# 生成 .env 文件
pnpm run aws:env:gen

# 查看生成的配置
cat .env
```

生成的 `.env` 包含：
- S3 存储桶信息
- CloudFront CDN 配置
- RDS 数据库连接
- ElastiCache Redis 配置

### Azure 环境变量

```bash
# 生成 .env.azure 文件
pnpm run azure:env:gen

# 查看生成的配置
cat .env.azure
```

生成的 `.env.azure` 包含：
- Storage Account 信息
- Azure CDN 配置
- PostgreSQL 数据库连接
- Azure Redis 配置

## 🎯 部署内容对比

| 组件 | AWS | Azure |
|------|-----|-------|
| **网络** | VPC + Subnets | Virtual Network + Subnets |
| **计算** | ECS Fargate | Container Apps |
| **数据库** | RDS PostgreSQL | PostgreSQL Flexible Server |
| **缓存** | ElastiCache Redis | Azure Cache for Redis |
| **存储** | S3 | Storage Account + Blob |
| **CDN** | CloudFront | Azure CDN |
| **容器注册** | ECR | Container Registry |
| **DNS** | Route53 | Azure DNS |

## 🔍 常用检查命令

### AWS 检查

```bash
cd projects/aws

# 查看输出
pnpm run stack output

# 查看配置
pnpm run config

# 刷新状态
pnpm run refresh
```

### Azure 检查

```bash
cd projects/azure

# 查看输出
pnpm run stack output

# 查看配置
pnpm run config

# 刷新状态
pnpm run refresh
```

## 🚨 故障排查

### 常见问题

1. **依赖安装失败**
   ```bash
   # 清理并重新安装
   pnpm run clean
   pnpm install
   ```

2. **Stack 冲突**
   ```bash
   # 查看当前 stack
   cd projects/aws
   pnpm run stack ls

   # 切换到正确的 stack
   pnpm run stack select your-stack-name
   ```

3. **配置缺失**
   ```bash
   # 检查必需配置
   cd projects/aws
   pnpm run config get projectName
   pnpm run config get aws:region

   cd projects/azure
   pnpm run config get projectName
   pnpm run config get azure-native:location
   ```

## ⚠️ 注意事项

1. **项目隔离**：AWS 和 Azure 项目完全独立，有各自的依赖和配置
2. **Stack 管理**：每个项目维护自己的 stack，避免冲突
3. **环境变量**：AWS 生成 `.env`，Azure 生成 `.env.azure`
4. **工作目录**：从根目录使用统一脚本，或进入子项目直接操作
5. **依赖更新**：每个子项目可以独立更新 Pulumi 提供商版本

## 🎉 架构优势

✅ **完全隔离**：AWS 和 Azure 项目互不影响
✅ **独立依赖**：每个项目只安装需要的 Pulumi 提供商
✅ **统一接口**：根项目提供一致的命令接口
✅ **灵活切换**：可在根目录或子项目中操作
✅ **测试友好**：最小配置用于快速验证
✅ **扩展性强**：轻松添加新的云平台项目

这种架构让你可以：
- 针对不同云平台优化代码结构
- 独立管理每个平台的依赖版本
- 保持配置和状态的完全隔离
- 轻松扩展到更多云平台