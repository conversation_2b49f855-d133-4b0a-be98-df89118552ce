# Database Configuration
DATABASE_URL=*********************************************************/maindb
DB_HOST=YOUR_DB_ENDPOINT
DB_PORT=5432
DB_NAME=maindb
DB_USER=postgres
DB_PASSWORD=YOUR_PASSWORD

# Redis Cache Configuration
REDIS_URL=redis://YOUR_REDIS_ENDPOINT:6379
REDIS_HOST=YOUR_REDIS_ENDPOINT
REDIS_PORT=6379

# AWS Secrets Manager
DB_SECRET_ARN=YOUR_DB_SECRET_ARN

# Project Configuration
PROJECT_NAME=a1d

# Instructions:
# 1. Copy this file to .env.local in your project
# 2. Run: pulumi stack output envConfig --show-secrets
# 3. Parse the JSON output and update the values above
# 4. Or use the generate-env.js script to automate this