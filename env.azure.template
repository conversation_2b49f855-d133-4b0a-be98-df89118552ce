# Azure Environment Variables Template
# Copy this file to .env.azure and fill in the values

# Platform Configuration
PLATFORM=azure
PROJECT_NAME=YOUR_PROJECT_NAME

# Database Configuration (Azure Database for PostgreSQL)
DATABASE_URL=*****************************************************************************************/maindb?sslmode=require
POSTGRES_URL=*****************************************************************************************/maindb?sslmode=require
DB_HOST=YOUR_POSTGRES_SERVER.postgres.database.azure.com
DB_PORT=5432
DB_NAME=maindb
DB_USER=postgres
DB_PASSWORD=YOUR_PASSWORD

# Redis Cache Configuration (Azure Cache for Redis)
REDIS_URL=rediss://:YOUR_REDIS_KEY@YOUR_REDIS_NAME.redis.cache.windows.net:6380
REDIS_HOST=YOUR_REDIS_NAME.redis.cache.windows.net
REDIS_PORT=6380

# Storage Configuration (Azure Blob Storage)
STORAGE_ACCOUNT_NAME=YOUR_STORAGE_ACCOUNT
STORAGE_CONTAINER_NAME=static
CDN_URL=https://YOUR_CDN_ENDPOINT.azureedge.net

# Container Registry (Azure Container Registry)
ACR_LOGIN_SERVER=YOUR_ACR_NAME.azurecr.io

# Azure Resources (for direct access if needed)
RESOURCE_GROUP_NAME=YOUR_RESOURCE_GROUP
LOCATION=East Asia
VNET_ID=/subscriptions/YOUR_SUBSCRIPTION/resourceGroups/YOUR_RG/providers/Microsoft.Network/virtualNetworks/YOUR_VNET
PUBLIC_SUBNET_ID=/subscriptions/YOUR_SUBSCRIPTION/resourceGroups/YOUR_RG/providers/Microsoft.Network/virtualNetworks/YOUR_VNET/subnets/public
PRIVATE_SUBNET_ID=/subscriptions/YOUR_SUBSCRIPTION/resourceGroups/YOUR_RG/providers/Microsoft.Network/virtualNetworks/YOUR_VNET/subnets/private
POSTGRES_SERVER_NAME=YOUR_POSTGRES_SERVER
POSTGRES_FQDN=YOUR_POSTGRES_SERVER.postgres.database.azure.com
REDIS_NAME=YOUR_REDIS_NAME
REDIS_HOSTNAME=YOUR_REDIS_NAME.redis.cache.windows.net
ACR_NAME=YOUR_ACR_NAME

# Instructions:
# 1. Copy this file to .env.azure in your project
# 2. Run: pnpm run azure:env:gen
# 3. Or manually run: pulumi stack output envConfig --show-secrets
# 4. Parse the JSON output and update the values above
