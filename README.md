# a1d-pulumi

多云共享基础设施 - 支持 AWS 和 Azure 平台的 PostgreSQL 数据库、Redis 缓存和对象存储（开发环境）

## ⚠️ 重要说明

**本项目专为开发环境设计**，为了简化配置和多项目共享：
- 数据库密码直接通过环境变量传递
- 无需 AWS Secrets Manager
- 适合团队内部开发使用
- **请勿用于生产环境**

## 功能

### 🌐 多云支持
- **AWS 平台** - 成熟稳定的云服务
- **Azure 平台** - 微软云生态集成

### 🗄️ 数据库服务
- **AWS**: PostgreSQL RDS (PostgreSQL 17.5, ARM64)
- **Azure**: Database for PostgreSQL Flexible Server (PostgreSQL 17, ARM64)

### 🚀 缓存服务
- **AWS**: Redis ElastiCache (ARM64)
- **Azure**: Cache for Redis (Redis 6)

### 📦 存储和 CDN
- **AWS**: S3 Bucket + CloudFront CDN
- **Azure**: Blob Storage + Azure CDN

### 🌐 网络基础设施
- **AWS**: VPC + 公有/私有子网
- **Azure**: Virtual Network + 公有/私有子网

### 🔧 开发特性
- 🔧 **多项目共享** - 一套基础设施，多个项目使用
- 🛠️ **开发友好** - 支持本地开发隧道
- ⚡ **ARM 架构优化** - 全栈 ARM64，性价比提升 30-40%
- 🐳 **容器支持** - ECR/ACR + Fargate/Container Apps

## 快速开始

### 选择云平台

#### 🟠 AWS 平台部署

**0. 配置 AWS 认证 (首次使用)**

如果这是你第一次使用 AWS CLI/SDK，请先阅读：📖 **[AWS 认证配置指南](./docs/aws/aws-auth-setup.md)**

**1. 部署 AWS 基础设施**

```bash
# 设置数据库密码（安全存储）
pulumi config set --secret dbPassword "your-secure-password"

# 可选：设置项目名称
pulumi config set projectName "your-project-name"

# 部署 AWS 基础设施
pulumi up
```

#### 🔵 Azure 平台部署

**0. 配置 Azure 认证 (首次使用)**

如果这是你第一次使用 Azure CLI，请先阅读：📖 **[Azure 认证配置指南](./docs/azure/azure-auth-setup.md)**

**1. 切换到 Azure 分支**

```bash
git checkout azure-environment
```

**2. 部署 Azure 基础设施**

```bash
# 创建 Azure stack
pulumi stack init azure-dev

# 设置 Azure 配置
pulumi config set azure-native:location "East Asia"
pulumi config set projectName "your-project-name"
pulumi config set --secret dbPassword "your-secure-password"

# 部署 Azure 基础设施
pnpm run azure:up
```

### 本地开发环境

#### AWS 本地开发

数据库和 Redis 部署在私有子网中，需要通过隧道访问：

📖 **[本地开发隧道指南](./docs/aws/aws-local-development-tunnels.md)** - 详细的本地访问方案

**为项目生成 .env.local**

```bash
# 在当前目录生成
bun run env:gen

# 或为特定项目目录生成
bun run env:gen /path/to/your/project
```

#### Azure 本地开发

Azure 数据库和 Redis 部署在私有网络中，支持 SSL 连接：

📖 **[Azure 部署指南](./docs/azure/azure-deployment-guide.md)** - 详细的 Azure 使用方案

**为项目生成 .env.azure**

```bash
# 在当前目录生成
pnpm run azure:env:gen

# 或为特定项目目录生成
pnpm run azure:env:gen /path/to/your/project
```

### 4. 在项目中使用

生成的 `.env.local` 包含：

```env
# Database Configuration (通过隧道访问)
POSTGRES_URL=postgresql://postgres:password@localhost:5432/maindb
DB_HOST=localhost
DB_PORT=5432
DB_NAME=maindb
DB_USER=postgres
DB_PASSWORD=your-secure-password

# Redis Cache Configuration (通过隧道访问)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# CDN Configuration
CDN_URL=https://d1234567890.cloudfront.net
CDN_DISTRIBUTION_ID=E1234567890
S3_BUCKET_NAME=a1d-dev-bucket

# Project Configuration
PROJECT_NAME=a1d-dev
```

## 架构说明

### 架构图

```mermaid
graph TB
    subgraph "AWS Cloud - Region: ap-southeast-2"
        subgraph "VPC (10.0.0.0/16)"
            subgraph "Public Subnets"
                ALB[Application Load Balancer<br/>Port 80/443]
                BASTION[Bastion Host<br/>t4g.nano<br/>Session Manager Only]

                subgraph "Fargate Services"
                    FARGATE[ECS Fargate<br/>Container Apps]
                end
            end

            subgraph "Private Subnets"
                subgraph "Data Layer"
                    RDS[(PostgreSQL 17.5<br/>db.t4g.micro ARM64<br/>20GB Storage)]
                    REDIS[(Redis 7<br/>cache.t4g.micro ARM64<br/>ElastiCache)]
                end
            end
        end

        subgraph "Storage & CDN"
            S3[S3 Bucket<br/>Object Storage]
            ECR[ECR Repository<br/>Container Images]
            CDN[CloudFront CDN<br/>Global Distribution<br/>Edge Caching]
        end

        subgraph "Security & Network"
            OAI[CloudFront OAI<br/>S3 Access Control]
            SG_APP[Security Group<br/>Fargate Apps]
            SG_DB[Security Group<br/>Database]
            SG_CACHE[Security Group<br/>Cache]
            SG_BASTION[Security Group<br/>Bastion]
        end
    end

    subgraph "Local Development"
        DEV[Developer Machine]
        TUNNEL[SSH Tunnel<br/>via Session Manager]
    end

    subgraph "Internet"
        USERS[End Users]
    end

    %% Connections
    USERS -->|HTTPS| CDN
    CDN -->|Origin Request| S3
    OAI -.->|Authorize| CDN
    USERS -->|HTTPS| ALB
    ALB -->|HTTP| FARGATE
    FARGATE -->|5432| RDS
    FARGATE -->|6379| REDIS
    FARGATE -->|API| S3
    FARGATE -->|Pull Images| ECR

    BASTION -->|5432| RDS
    BASTION -->|6379| REDIS

    DEV -->|Session Manager| TUNNEL
    TUNNEL -->|Port Forward| BASTION

    %% Security Group Rules
    SG_APP -.->|Controls| FARGATE
    SG_DB -.->|Controls| RDS
    SG_CACHE -.->|Controls| REDIS
    SG_BASTION -.->|Controls| BASTION

    %% Styling
    classDef aws fill:#FF9900,stroke:#232F3E,stroke-width:2px,color:#fff
    classDef storage fill:#569A31,stroke:#232F3E,stroke-width:2px,color:#fff
    classDef compute fill:#FF9900,stroke:#232F3E,stroke-width:2px,color:#fff
    classDef network fill:#7AA116,stroke:#232F3E,stroke-width:2px,color:#fff
    classDef security fill:#DD344C,stroke:#232F3E,stroke-width:2px,color:#fff
    classDef local fill:#4B8BBE,stroke:#232F3E,stroke-width:2px,color:#fff
    classDef user fill:#646464,stroke:#232F3E,stroke-width:2px,color:#fff

    class RDS,REDIS aws
    class S3,ECR,CDN storage
    class FARGATE,BASTION,ALB compute
    class VPC network
    class OAI,SG_APP,SG_DB,SG_CACHE,SG_BASTION security
    class DEV,TUNNEL local
    class USERS user
```

### 网络架构
- **应用**: 部署在公有子网，可直接访问互联网
- **数据库**: 部署在私有子网，只允许 VPC 内访问
- **Redis**: 部署在私有子网，只允许 VPC 内访问
- **CloudFront CDN**: 全球分发静态资源，通过 OAI 安全访问 S3
- **安全**: 应用可访问数据库，外部无法直接访问

### 成本优化
- ✅ **ARM 架构优势**: 使用 Graviton2 (t4g) 实例，性价比提升 40%
- ✅ **无 NAT Gateway**: 数据库不需要访问互联网，省去 $45/月
- ✅ **t4g.micro 实例**: 适合开发和小型生产环境，成本更低
- ✅ **按需访问**: 本地开发时才建立隧道连接

## 安全最佳实践

### 开发环境密码管理

由于这是开发环境专用配置，我们采用简化的密码管理方案：

1. **密码存储**: 使用 Pulumi 的 secret 配置存储密码
2. **本地开发**: 通过 `.env.local` 文件管理（已加入 .gitignore）
3. **多项目共享**: 所有项目使用相同的开发数据库密码
4. **生产环境**: 如需部署生产环境，请使用 AWS Secrets Manager 或其他密钥管理服务

```bash
# 设置开发环境数据库密码
pulumi config set --secret dbPassword "your-dev-password"
```

## 多项目共享

### 工作流

1. **部署基础设施**: `pulumi up`
2. **启动本地隧道**: 参考 [本地开发隧道指南](./docs/aws/aws-local-development-tunnels.md)
3. **多个项目使用**: `bun run generate-env ../project-x`

## 成本优化

- 使用 `t3.micro` 实例（适合开发/小规模生产）
- 开发模式无需 Bastion 主机
- 按需扩展配置

## 💰 成本估算

### 月度成本预估 (ap-southeast-2 区域)

| 资源类型 | 配置 | 月度成本 | 说明 |
|----------|------|----------|------|
| **RDS PostgreSQL** | db.t4g.micro (ARM), 20GB | $9.49 | PostgreSQL 17.5, Graviton2 |
| **ElastiCache Redis** | cache.t4g.micro (ARM), 1节点 | $8.47 | 单节点配置, Graviton2 |
| **S3 存储** | 标准存储 | $0.025/GB | 按使用量计费 |
| **CloudFront CDN** | PriceClass_100 | $0.085/GB | 北美/欧洲边缘节点 |
| **VPC** | 基础网络 | 免费 | 无 NAT Gateway |
| **ECR** | 容器镜像存储 | $0.10/GB | 按存储量计费 |
| **数据传输** | 应用出站流量 | $0.114/GB | 前 1GB 免费 |
| **CloudWatch 日志** | 基础监控 | $0.50 | 标准日志保留 |
| | | | |
| **基础成本 (不含数据)** | | **~$18.50/月** | 不含存储使用量 |

### 成本优化建议

#### 架构优化
- ✅ **ARM 架构 (Graviton2)**: 相比 x86 节省 ~32% 成本，性能提升 40%
- ✅ **无 NAT Gateway**: 数据库不需要互联网访问，省去 $45/月
- ✅ **免费层**: RDS 和 ElastiCache 都有 12 个月免费层
- ✅ **按需启停**: 开发时可以停止 RDS 实例 (7天内免费)
- ✅ **本地开发**: 通过隧道访问，无额外网络费用
- ✅ **CloudFront 价格等级**: 使用 PriceClass_100 (北美/欧洲)，节省 30% CDN 成本

#### 生产环境升级
```bash
# 可选的生产优化配置
instanceClass: "db.t4g.small"    # 数据库升级 (+$9.49/月)
numCacheClusters: 2             # Redis 高可用 (+$8.47/月)
```

- 🔄 **自动备份**: RDS 7天备份已包含
- 📊 **监控**: CloudWatch 基础监控免费
- 🔐 **加密**: 静态加密无额外费用

### 扩展成本预估

| 升级方案 | 额外成本/月 | 适用场景 |
|----------|-------------|----------|
| **数据库升级 t4g.small** | +$9.49 | 中等负载 |
| **Redis 双节点高可用** | +$8.47 | 生产环境 |
| **RDS 备份延长 (30天)** | +$2.00 | 合规要求 |
| **多可用区部署** | +100% | 高可用需求 |
| **监控增强 (CloudWatch)** | +$5-15 | 详细监控 |

### 实际使用成本示例

#### 小型项目 (1-2个应用)
- **基础设施**: $18.50/月
- **数据存储**: $5/月
- **应用流量**: $2/月 (20GB 出站)
- **CDN 流量**: $1.70/月 (20GB CDN)
- **总计**: **~$27/月**

#### 中型项目 (3-5个应用)
- **基础设施**: $18.50/月 (可升级到 $36.50/月)
- **数据存储**: $15/月
- **应用流量**: $11/月 (100GB 出站)
- **CDN 流量**: $8.50/月 (100GB CDN)
- **总计**: **~$53/月** (基础) / **~$71/月** (升级)

#### 对比其他方案

| 方案 | 月成本 | 维护工作 | 可扩展性 |
|------|--------|----------|----------|
| **本项目 (AWS托管)** | $19-63 | 极低 | 高 |
| **自建服务器** | $50-200 | 高 | 中 |
| **Heroku Postgres** | $50-500 | 低 | 中 |
| **MongoDB Atlas** | $57-590 | 低 | 高 |
| **Supabase** | $25-100 | 极低 | 中 |

### 🎯 成本控制技巧

1. **按需使用**
```bash
# 开发完停机 (7天内免费)
aws rds stop-db-instance --db-instance-identifier postgres-db

# 本地开发时关闭隧道
# 只在需要时连接数据库
```

2. **预留实例折扣** (生产环境)
```bash
# 1-3年预付费可节省 30-60%
# RDS: 节省 30-60%
# ElastiCache: 节省 30-55%
```

3. **监控和警报**
```bash
# 设置成本警报
aws budgets create-budget --budget file://cost-budget.json
```

4. **CloudFront 缓存优化**
```bash
# 为静态资源设置长缓存时间
# JS/CSS/图片: 1年缓存 (已配置)
# API 响应: 不缓存 (已配置)
# 使用 Cache-Control headers 优化缓存命中率
```

### 免费层福利 (12个月)

- 🆓 **RDS**: 750小时 db.t4g.micro + 20GB 存储
- 🆓 **ElastiCache**: 750小时 cache.t4g.micro
- 🆓 **S3**: 5GB 标准存储 + 20,000 GET/2,000 PUT
- 🆓 **CloudFront**: 1TB 数据传输 + 10,000,000 HTTP/HTTPS 请求
- 🆓 **VPC**: 5GB 数据传输
- 🆓 **CloudWatch**: 10个指标 + 5GB 日志

**新用户第一年约省**：**$222** ($18.50/月)

## 输出信息

```bash
# 查看所有输出
pulumi stack output

# 查看数据库连接信息
pulumi stack output postgresEndpoint

# 查看 Redis 连接信息
pulumi stack output redisEndpoint

# 查看 CloudFront CDN 信息
pulumi stack output cloudFrontUrl
pulumi stack output cloudFrontDistributionId

# 查看 S3 存储桶信息
pulumi stack output bucketName

# 查看包含密码的配置 (用于生成 .env.local)
pulumi stack output envConfig --show-secrets
```

## 📚 文档

- 📖 [AWS 认证配置指南](./docs/aws/aws-auth-setup.md) - 本地开发环境 AWS CLI 配置
- 🚇 [本地开发隧道指南](./docs/aws/aws-local-development-tunnels.md) - 详细的本地数据库访问方案
- 🐳 [Fargate 应用部署指南](./examples/fargate-app) - 容器化应用部署到 ECS Fargate