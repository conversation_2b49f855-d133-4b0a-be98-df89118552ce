---
description: Scripts directory containing TypeScript utilities for project automation and environment setup
globs: scripts/**/*.ts
alwaysApply: false
---
# Scripts Directory Rules

## Overview
TypeScript utility scripts executed via Bun runtime for project automation.

## Development Standards


### Technical Stack
- **Runtime**: Bun
- **Language**: TypeScript with `/// <reference types="@types/bun" />`

### Code Conventions
- File naming: `kebab-case.ts`
- Export named symbols, avoid `export default`
- Complex logic: use named functions, not lambdas
- Entry point: implement `main()` function
- Shell execution: Use Bun's `$` for running shell commands
- API preference: Prefer Bun native APIs over external dependencies when possible

### Script Structure
```typescript
/// <reference types="@types/bun" />
import { $ } from 'bun'
import { /* other dependencies */ } from '/* paths */'

function main() {
  console.log('Starting...')
  // implementation
  console.log('✅ Completed')
}

main()
```

## Execution
```bash
bun run scripts/<script-name>.ts
```

Dependencies assumed pre-installed. Scripts provide clear execution feedback via console output.
