# Docker 构建和推送指南

## 概述

本指南介绍如何为 Fargate 应用构建 Docker 镜像并推送到 AWS ECR，使用 pnpm 作为包管理器以获得更好的性能和更小的镜像体积。

## 前置条件

1. ✅ 已部署基础设施项目 (包含 ECR 仓库)
2. ✅ 已安装 Docker
3. ✅ 已配置 AWS CLI
4. ✅ 应用已构建 (存在 `dist/` 目录)

## 项目结构

```
fargate-app/
├── src/                    # 源代码
├── dist/                   # 构建产物 (必需)
├── package.json
├── pnpm-lock.yaml         # pnpm 锁定文件
├── tsconfig.json
├── Dockerfile              # Docker 构建文件
├── .dockerignore          # Docker 忽略文件
└── scripts/
    └── build-and-push.sh   # 构建推送脚本
```

## 快速开始

### 1. 构建应用

```bash
# 编译 TypeScript 到 dist/ 目录
pnpm build
```

### 2. 构建并推送 Docker 镜像

```bash
# 使用脚本一键构建和推送
chmod +x scripts/build-and-push.sh
./scripts/build-and-push.sh

# 或者推送带标签的版本
./scripts/build-and-push.sh v1.2.3
```

### 3. 部署到 Fargate

```bash
# 部署更新
pulumi up
```

## 手动构建步骤

如果您想手动执行每个步骤：

### 1. 获取 ECR 信息

```bash
# 从基础设施栈获取 ECR 仓库 URL
ECR_URL=$(pulumi stack output ecrRepositoryUrl -s ethan-huo-org/a1d-pulumi/dev)
REGION="ap-southeast-2"
```

### 2. 登录 ECR

```bash
# 登录到 AWS ECR
aws ecr get-login-password --region $REGION | \
  docker login --username AWS --password-stdin $(echo $ECR_URL | cut -d'/' -f1)
```

### 3. 构建镜像

```bash
# 构建 ARM64 Docker 镜像
docker build --platform=linux/arm64 -t my-app:latest .

# 或者使用 buildx 进行多架构构建 (可选)
docker buildx build --platform=linux/arm64 -t my-app:latest .
```

### 4. 标记镜像

```bash
# 为 ECR 标记镜像
docker tag my-app:latest $ECR_URL:latest
```

### 5. 推送镜像

```bash
# 推送到 ECR
docker push $ECR_URL:latest
```

## Dockerfile 说明

我们的 Dockerfile 使用多阶段构建和 pnpm 最佳实践，针对 ARM64 架构优化镜像大小和构建速度：

```dockerfile
# 第一阶段：基础环境 (ARM64)
FROM --platform=linux/arm64 node:20-alpine AS base
RUN apk add --no-cache curl && corepack enable
WORKDIR /app

# 第二阶段：安装生产依赖
FROM base AS deps
COPY package.json pnpm-lock.yaml ./
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm fetch --frozen-lockfile
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm install --frozen-lockfile --prod

# 第三阶段：构建应用
FROM base AS build
COPY package.json pnpm-lock.yaml ./
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm fetch --frozen-lockfile
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm install --frozen-lockfile
COPY . .
RUN pnpm build

# 第四阶段：生产镜像 (ARM64)
FROM --platform=linux/arm64 node:20-alpine AS production
RUN apk add --no-cache curl && corepack enable
WORKDIR /app
# 复制依赖和构建产物
COPY --from=deps /app/node_modules ./node_modules
COPY --from=build /app/dist ./dist
# 设置非 root 用户
USER nodejs
# 启动应用
CMD ["node", "dist/index.js"]
```

**ARM64 + pnpm 优化特性**：
- 使用 ARM64 Alpine Linux (更小更快的镜像)
- 四阶段构建 (最大化缓存复用)
- BuildKit cache mounts (加速依赖安装)
- `pnpm fetch` (优化依赖缓存)
- 非 root 用户 (提高安全性)
- 健康检查支持
- ARM64 原生性能优势

## 环境变量

应用在容器中可以访问以下环境变量：

| 变量名 | 来源 | 说明 |
|--------|------|------|
| `NODE_ENV` | 容器配置 | 运行环境 |
| `DB_ENDPOINT` | 基础设施栈 | PostgreSQL 端点 |
| `REDIS_ENDPOINT` | 基础设施栈 | Redis 端点 |
| `DB_SECRET_ARN` | AWS Secrets Manager | 数据库凭证 |

## 镜像标签策略

### 开发环境
```bash
# 使用 latest 标签
./scripts/build-and-push.sh latest
```

### 生产环境
```bash
# 使用 Git commit SHA
./scripts/build-and-push.sh $(git rev-parse --short HEAD)

# 使用语义化版本
./scripts/build-and-push.sh v1.2.3
```

## 故障排除

### 常见问题

#### 1. ECR 登录失败
```bash
# 检查 AWS 凭证
aws sts get-caller-identity

# 检查区域设置
aws configure get region
```

#### 2. 构建失败 - 找不到 dist 目录
```bash
# 确保已构建应用
pnpm build
ls -la dist/
```

#### 3. 推送失败 - 权限不足
```bash
# 检查 IAM 权限，确保有 ECR 推送权限
aws ecr describe-repositories
```

#### 4. 容器启动失败
```bash
# 检查健康检查端点
curl http://localhost:3000/health

# 查看容器日志
docker logs <container-id>
```

#### 5. pnpm 缓存问题
```bash
# 清理 pnpm 缓存
pnpm store prune

# 重新安装依赖
rm -rf node_modules pnpm-lock.yaml
pnpm install
```

### 本地测试

```bash
# 本地运行容器测试 (ARM64)
docker run --platform=linux/arm64 -p 3000:3000 \
  -e NODE_ENV=development \
  -e DB_ENDPOINT=localhost \
  -e REDIS_ENDPOINT=localhost \
  my-app:latest

# 测试健康检查
curl http://localhost:3000/health

# 如果在 M1/M2 Mac 上，可以直接运行 (原生 ARM64)
docker run -p 3000:3000 \
  -e NODE_ENV=development \
  -e DB_ENDPOINT=localhost \
  -e REDIS_ENDPOINT=localhost \
  my-app:latest
```

## 自动化 CI/CD

### GitHub Actions 示例

```yaml
name: Build and Deploy
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 9

      - name: Install and Build
        run: |
          pnpm install --frozen-lockfile
          pnpm build

      - name: Configure AWS
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-southeast-2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and Push to ECR (ARM64)
        run: |
          chmod +x scripts/build-and-push.sh
          ./scripts/build-and-push.sh ${{ github.sha }}

      - name: Deploy with Pulumi
        uses: pulumi/actions@v4
        with:
          command: up
          stack-name: production
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
```

## 性能优化

### ARM64 架构优势
- **CPU 性能**: Graviton2 处理器性价比提升 40%
- **内存效率**: 更好的内存带宽和缓存性能
- **能耗更低**: 绿色计算，降低运行成本
- **原生支持**: Node.js 在 ARM64 上有原生优化

### pnpm 特有优化
- **符号链接存储**: pnpm 使用全局存储减少磁盘占用
- **更快的安装**: 并行安装和智能缓存
- **严格的依赖管理**: 避免幽灵依赖
- **BuildKit 缓存**: 利用 Docker 缓存层加速构建

### 镜像大小优化
- **ARM64 Alpine 基础镜像**: 最小化基础镜像体积
- **多阶段构建**: 分离构建和运行环境
- **生产依赖**: 只安装运行时必需依赖
- **缓存层**: 优化 Docker 层缓存策略

## 成本优化

### Fargate ARM64 成本优势
- **计算成本**: ARM64 Fargate 比 x86 便宜约 20%
- **性能提升**: 相同价格下获得更好的性能
- **能源效率**: 减少碳排放，符合可持续发展

### 镜像和构建优化
- **镜像大小**: 使用 ARM64 Alpine 基础镜像和多阶段构建
- **生命周期**: 自动清理旧镜像 (保留最新 10 个)
- **压缩**: ECR 自动压缩镜像层
- **传输**: 同区域 ECR 到 Fargate 传输免费
- **缓存**: BuildKit 缓存减少重复构建时间

## 安全最佳实践

1. **最小权限**: ECR 策略只允许必要操作
2. **镜像扫描**: 自动扫描安全漏洞
3. **非 root 用户**: 容器以 nodejs 用户运行
4. **密钥管理**: 使用 AWS Secrets Manager
5. **网络隔离**: 容器在私有子网中运行
6. **依赖审计**: 定期运行 `pnpm audit` 检查漏洞

## pnpm 特有注意事项

### 1. 锁定文件
确保 `pnpm-lock.yaml` 文件被提交到版本控制，这是 pnpm 的锁定文件。

### 2. .dockerignore 配置
```dockerignore
node_modules
.git
.gitignore
*.md
dist
.pnpm-store
```

### 3. 构建缓存
利用 BuildKit 的缓存挂载功能，可以显著提升后续构建速度：
```dockerfile
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
```

### 4. 单一仓库支持
如果使用 pnpm workspace，可以使用 `pnpm deploy` 命令优化部署：
```bash
pnpm deploy --filter=app --prod /output
```