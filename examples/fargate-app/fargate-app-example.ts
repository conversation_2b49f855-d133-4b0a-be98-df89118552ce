// Example Fargate Application Project
// File: fargate-app/src/index.ts

import * as pulumi from "@pulumi/pulumi";
import * as aws from "@pulumi/aws";

// Reference the infrastructure stack
const infraStack = new pulumi.StackReference("ethan-huo-org/a1d-pulumi/dev");

// Get infrastructure outputs
const vpcId = infraStack.getOutput("vpcId");
const privateSubnetIds = infraStack.getOutput("privateSubnetIds");
const publicSubnetIds = infraStack.getOutput("publicSubnetIds");
const fargateSecurityGroupId = infraStack.getOutput("fargateSecurityGroupId");
const dbSecretArn = infraStack.getOutput("dbSecretArn");
const postgresEndpoint = infraStack.getOutput("postgresEndpoint");
const redisEndpoint = infraStack.getOutput("redisEndpoint");
const ecrRepositoryUrl = infraStack.getOutput("ecrRepositoryUrl");

// Create IAM role for ECS task execution
const executionRole = new aws.iam.Role("ecs-execution-role", {
	assumeRolePolicy: JSON.stringify({
		Version: "2012-10-17",
		Statement: [
			{
				Action: "sts:AssumeRole",
				Effect: "Allow",
				Principal: {
					Service: "ecs-tasks.amazonaws.com",
				},
			},
		],
	}),
});

// Attach execution role policy
new aws.iam.RolePolicyAttachment("ecs-execution-role-policy", {
	role: executionRole.name,
	policyArn:
		"arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy",
});

// Create IAM role for ECS task
const taskRole = new aws.iam.Role("ecs-task-role", {
	assumeRolePolicy: JSON.stringify({
		Version: "2012-10-17",
		Statement: [
			{
				Action: "sts:AssumeRole",
				Effect: "Allow",
				Principal: {
					Service: "ecs-tasks.amazonaws.com",
				},
			},
		],
	}),
});

// Attach secrets manager access policy to task role
new aws.iam.RolePolicyAttachment("ecs-task-secrets-policy", {
	role: taskRole.name,
	policyArn: "arn:aws:iam::aws:policy/SecretsManagerReadWrite",
});

// Create CloudWatch Log Group
const logGroup = new aws.cloudwatch.LogGroup("app-logs", {
	name: "/ecs/fargate-app",
	retentionInDays: 7,
});

// Create security group for ALB
const albSecurityGroup = new aws.ec2.SecurityGroup("alb-sg", {
	vpcId: vpcId,
	description: "Security group for Application Load Balancer",
	ingress: [
		{
			fromPort: 80,
			toPort: 80,
			protocol: "tcp",
			cidrBlocks: ["0.0.0.0/0"],
			description: "HTTP access",
		},
		{
			fromPort: 443,
			toPort: 443,
			protocol: "tcp",
			cidrBlocks: ["0.0.0.0/0"],
			description: "HTTPS access",
		},
	],
	egress: [
		{
			fromPort: 0,
			toPort: 0,
			protocol: "-1",
			cidrBlocks: ["0.0.0.0/0"],
		},
	],
});

// Create Application Load Balancer
const alb = new aws.lb.LoadBalancer("app-alb", {
	internal: false,
	loadBalancerType: "application",
	securityGroups: [albSecurityGroup.id],
	subnets: publicSubnetIds,
	enableDeletionProtection: false,
});

// Create Target Group
const targetGroup = new aws.lb.TargetGroup("app-target-group", {
	port: 3000,
	protocol: "HTTP",
	vpcId: vpcId,
	targetType: "ip",
	healthCheck: {
		enabled: true,
		path: "/health",
		port: "3000",
		protocol: "HTTP",
		healthyThreshold: 2,
		unhealthyThreshold: 5,
		timeout: 30,
		interval: 60,
	},
});

// Create ALB Listener
const listener = new aws.lb.Listener("app-listener", {
	loadBalancerArn: alb.arn,
	port: 80,
	protocol: "HTTP",
	defaultActions: [
		{
			type: "forward",
			targetGroupArn: targetGroup.arn,
		},
	],
});

// Create ECS Cluster
const cluster = new aws.ecs.Cluster("app-cluster", {
	name: "fargate-app-cluster",
});

// Create Task Definition
const taskDefinition = new aws.ecs.TaskDefinition("app-task", {
	family: "fargate-app",
	cpu: "256",
	memory: "512",
	networkMode: "awsvpc",
	requiresCompatibilities: ["FARGATE"],
	runtimePlatform: {
		cpuArchitecture: "ARM64",
		operatingSystemFamily: "LINUX",
	},
	executionRoleArn: executionRole.arn,
	taskRoleArn: taskRole.arn,
	containerDefinitions: pulumi.jsonStringify([
		{
			name: "app",
			image: pulumi.interpolate`${ecrRepositoryUrl}:latest`, // 使用 ECR 镜像 (pnpm 构建)
			portMappings: [
				{
					containerPort: 3000,
					protocol: "tcp",
				},
			],
			environment: [
				{
					name: "NODE_ENV",
					value: "production",
				},
				{
					name: "DB_ENDPOINT",
					value: postgresEndpoint,
				},
				{
					name: "REDIS_ENDPOINT",
					value: redisEndpoint,
				},
			],
			secrets: [
				{
					name: "DB_SECRET",
					valueFrom: dbSecretArn,
				},
			],
			logConfiguration: {
				logDriver: "awslogs",
				options: {
					"awslogs-group": logGroup.name,
					"awslogs-region": "ap-southeast-2",
					"awslogs-stream-prefix": "ecs",
				},
			},
		},
	]),
});

// Create ECS Service
const service = new aws.ecs.Service(
	"app-service",
	{
		cluster: cluster.arn,
		taskDefinition: taskDefinition.arn,
		launchType: "FARGATE",
		desiredCount: 1,
		networkConfiguration: {
			subnets: privateSubnetIds,
			securityGroups: [fargateSecurityGroupId],
			assignPublicIp: false,
		},
		loadBalancers: [
			{
				targetGroupArn: targetGroup.arn,
				containerName: "app",
				containerPort: 3000,
			},
		],
	},
	{ dependsOn: [listener] }
);

// Exports
export const appUrl = alb.dnsName;
export const clusterName = cluster.name;
export const taskDefinitionArn = taskDefinition.arn;
