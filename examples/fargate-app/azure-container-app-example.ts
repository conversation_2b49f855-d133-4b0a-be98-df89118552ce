// Example Azure Container App Project (Best Practices)
// File: azure-container-app/src/index.ts

import * as pulumi from "@pulumi/pulumi";
import * as azure from "@pulumi/azure-native";

// Reference the infrastructure stack
const infraStack = new pulumi.StackReference("ethan-huo-org/a1d-pulumi/azure-dev");

// Get infrastructure outputs
const resourceGroupName = infraStack.getOutput("resourceGroupName");
const location = infraStack.getOutput("resourceGroupLocation");
const vnetId = infraStack.getOutput("vnetId");
const publicSubnetId = infraStack.getOutput("publicSubnetId");
const privateSubnetId = infraStack.getOutput("privateSubnetId");
const containerAppsEnvironmentId = infraStack.getOutput("containerAppsEnvironmentId");
const acrLoginServer = infraStack.getOutput("acrLoginServer");
const postgresServerFqdn = infraStack.getOutput("postgresServerFqdn");
const redisHostname = infraStack.getOutput("redisHostname");
const keyVaultUri = infraStack.getOutput("keyVaultUri");
const storageAccountName = infraStack.getOutput("azureStorageAccountName");

// Project configuration
const projectName = "myapp";
const containerImage = `${acrLoginServer}/myapp:latest`;

// Create User-Assigned Managed Identity for the Container App
const managedIdentity = new azure.managedidentity.UserAssignedIdentity(`${projectName}-identity`, {
    resourceGroupName: resourceGroupName,
    location: location,
    userAssignedIdentityName: `${projectName}-identity`,
});

// Create RBAC assignments for the Managed Identity
// 1. ACR Pull permission
const acrPullAssignment = new azure.authorization.RoleAssignment(`${projectName}-acr-pull`, {
    principalId: managedIdentity.principalId,
    principalType: "ServicePrincipal",
    roleDefinitionId: pulumi.interpolate`/subscriptions/${azure.authorization.getClientConfig().subscriptionId}/providers/Microsoft.Authorization/roleDefinitions/7f951dda-4ed3-4680-a7ca-43fe172d538d`, // AcrPull
    scope: pulumi.interpolate`/subscriptions/${azure.authorization.getClientConfig().subscriptionId}/resourceGroups/${resourceGroupName}`,
});

// 2. Storage Blob Data Contributor
const storageContributorAssignment = new azure.authorization.RoleAssignment(`${projectName}-storage-contributor`, {
    principalId: managedIdentity.principalId,
    principalType: "ServicePrincipal",
    roleDefinitionId: pulumi.interpolate`/subscriptions/${azure.authorization.getClientConfig().subscriptionId}/providers/Microsoft.Authorization/roleDefinitions/ba92f5b4-2d11-453d-a403-e96b0029c9fe`, // Storage Blob Data Contributor
    scope: pulumi.interpolate`/subscriptions/${azure.authorization.getClientConfig().subscriptionId}/resourceGroups/${resourceGroupName}`,
});

// 3. Key Vault Secrets User
const keyVaultSecretsAssignment = new azure.authorization.RoleAssignment(`${projectName}-kv-secrets`, {
    principalId: managedIdentity.principalId,
    principalType: "ServicePrincipal",
    roleDefinitionId: pulumi.interpolate`/subscriptions/${azure.authorization.getClientConfig().subscriptionId}/providers/Microsoft.Authorization/roleDefinitions/4633458b-17de-408a-b874-0445c86b69e6`, // Key Vault Secrets User
    scope: pulumi.interpolate`/subscriptions/${azure.authorization.getClientConfig().subscriptionId}/resourceGroups/${resourceGroupName}`,
});

// Create Container App with Best Practices
const containerApp = new azure.app.ContainerApp(`${projectName}-app`, {
    resourceGroupName: resourceGroupName,
    location: location,
    containerAppName: `${projectName}-app`,
    managedEnvironmentId: containerAppsEnvironmentId,

    // Managed Identity Configuration
    identity: {
        type: azure.app.ManagedServiceIdentityType.UserAssigned,
        userAssignedIdentities: {
            [managedIdentity.id]: {},
        },
    },

    // Container Configuration
    configuration: {
        // Ingress Configuration (HTTPS only)
        ingress: {
            external: true,
            targetPort: 3000,
            traffic: [{
                latestRevision: true,
                weight: 100,
            }],
            transport: azure.app.IngressTransportMethod.Http,
        },

        // Registry Configuration (using Managed Identity)
        registries: [{
            server: acrLoginServer,
            identity: managedIdentity.id,
        }],

        // Secrets Configuration (Key Vault References)
        secrets: [
            {
                name: "database-password",
                keyVaultUrl: pulumi.interpolate`${keyVaultUri}secrets/database-password`,
                identity: managedIdentity.id,
            },
            {
                name: "redis-key",
                keyVaultUrl: pulumi.interpolate`${keyVaultUri}secrets/redis-primary-key`,
                identity: managedIdentity.id,
            },
        ],
    },

    // Template Configuration
    template: {
        // Auto-scaling Configuration
        scale: {
            minReplicas: 1,
            maxReplicas: 10,
            rules: [
                // HTTP-based scaling
                {
                    name: "http-scaling",
                    http: {
                        metadata: {
                            concurrentRequests: "50",
                        },
                    },
                },
                // Custom scaling based on Redis queue length (example)
                {
                    name: "redis-queue-scaling",
                    custom: {
                        type: "redis",
                        metadata: {
                            address: pulumi.interpolate`${redisHostname}:6380`,
                            listName: "task_queue",
                            listLength: "5",
                            enableTLS: "true",
                        },
                        auth: [{
                            secretRef: "redis-key",
                            triggerParameter: "password",
                        }],
                    },
                },
            ],
        },

        // Container Definition
        containers: [{
            name: "main",
            image: containerImage,

            // Resource limits
            resources: {
                cpu: 0.5,
                memory: "1Gi",
            },

            // Environment Variables
            env: [
                // Database Configuration (using Key Vault secret reference)
                {
                    name: "POSTGRES_HOST",
                    value: postgresServerFqdn,
                },
                {
                    name: "POSTGRES_PORT",
                    value: "5432",
                },
                {
                    name: "POSTGRES_USER",
                    value: "postgres",
                },
                {
                    name: "POSTGRES_DB",
                    value: "maindb",
                },
                {
                    name: "POSTGRES_PASSWORD",
                    secretRef: "database-password", // Key Vault reference
                },

                // Redis Configuration
                {
                    name: "REDIS_HOST",
                    value: redisHostname,
                },
                {
                    name: "REDIS_PORT",
                    value: "6380",
                },
                {
                    name: "REDIS_PASSWORD",
                    secretRef: "redis-key", // Key Vault reference
                },
                {
                    name: "REDIS_URL",
                    value: pulumi.interpolate`rediss://:$(REDIS_PASSWORD)@${redisHostname}:6380`,
                },

                // Storage Configuration
                {
                    name: "AZURE_STORAGE_ACCOUNT_NAME",
                    value: storageAccountName,
                },

                // Application Configuration
                {
                    name: "NODE_ENV",
                    value: "production",
                },
                {
                    name: "PORT",
                    value: "3000",
                },

                // Managed Identity Configuration
                {
                    name: "AZURE_CLIENT_ID",
                    value: managedIdentity.clientId,
                },
            ],

            // Health Probes
            probes: [
                {
                    type: azure.app.Type.Liveness,
                    httpGet: {
                        path: "/health",
                        port: 3000,
                        scheme: azure.app.Scheme.HTTP,
                    },
                    initialDelaySeconds: 30,
                    periodSeconds: 10,
                    timeoutSeconds: 5,
                    failureThreshold: 3,
                },
                {
                    type: azure.app.Type.Readiness,
                    httpGet: {
                        path: "/ready",
                        port: 3000,
                        scheme: azure.app.Scheme.HTTP,
                    },
                    initialDelaySeconds: 5,
                    periodSeconds: 5,
                    timeoutSeconds: 3,
                    failureThreshold: 3,
                },
            ],
        }],

        // Revision Configuration
        revisionSuffix: pulumi.interpolate`v${Date.now()}`,
    },

    // Tags
    tags: {
        Environment: "production",
        Application: projectName,
        ManagedBy: "pulumi",
    },
});

// Create Custom Domain (optional)
// const customDomain = new azure.app.ContainerAppsAuthConfig(`${projectName}-domain`, {
//     resourceGroupName: resourceGroupName,
//     containerAppName: containerApp.name,
//     authConfigName: "current",
//     // Custom domain configuration...
// });

// Export important values
export const containerAppName = containerApp.name;
export const containerAppFqdn = containerApp.latestRevisionFqdn;
export const containerAppUrl = pulumi.interpolate`https://${containerApp.latestRevisionFqdn}`;
export const managedIdentityId = managedIdentity.id;
export const managedIdentityClientId = managedIdentity.clientId;

// Export deployment information
export const deploymentInfo = {
    containerApp: containerApp.name,
    image: containerImage,
    environment: containerAppsEnvironmentId,
    managedIdentity: managedIdentity.clientId,
    url: pulumi.interpolate`https://${containerApp.latestRevisionFqdn}`,

    // Connection Information
    connections: {
        database: pulumi.interpolate`postgresql://postgres:<from-keyvault>@${postgresServerFqdn}:5432/maindb`,
        redis: pulumi.interpolate`rediss://:<from-keyvault>@${redisHostname}:6380`,
        storage: pulumi.interpolate`https://${storageAccountName}.blob.core.windows.net`,
    },
};

// Generate application deployment script
export const deploymentScript = pulumi.interpolate`#!/bin/bash
# Azure Container App Deployment Script
# Generated by Pulumi

set -e

echo "🚀 Deploying ${projectName} to Azure Container Apps..."

# Build and push container image
echo "📦 Building container image..."
docker build -t ${containerImage} .

echo "🔐 Logging into ACR..."
az acr login --name ${acrLoginServer.apply(s => s.split('.')[0])}

echo "📤 Pushing image to registry..."
docker push ${containerImage}

echo "🔄 Updating container app..."
az containerapp update \\
  --name ${containerApp.name} \\
  --resource-group ${resourceGroupName} \\
  --image ${containerImage}

echo "✅ Deployment completed!"
echo "🌐 Application URL: https://${containerApp.latestRevisionFqdn}"
`;