#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REGION="ap-southeast-2"
STACK_NAME="ethan-huo-org/a1d-pulumi/dev"

echo -e "${BLUE}🐳 Building and pushing Docker image to ECR...${NC}"

# Get ECR repository URL from infrastructure stack
echo -e "${YELLOW}📋 Getting ECR repository information...${NC}"
ECR_URL=$(pulumi stack output ecrRepositoryUrl -s $STACK_NAME)
if [ -z "$ECR_URL" ]; then
    echo -e "${RED}❌ Failed to get ECR repository URL. Make sure infrastructure is deployed.${NC}"
    exit 1
fi

ECR_REGISTRY=$(echo $ECR_URL | cut -d'/' -f1)
IMAGE_TAG=${1:-latest}

echo -e "${GREEN}✓ ECR URL: $ECR_URL${NC}"
echo -e "${GREEN}✓ Image tag: $IMAGE_TAG${NC}"

# Check if dist directory exists
if [ ! -d "dist" ]; then
    echo -e "${RED}❌ dist/ directory not found. Please run 'pnpm build' first.${NC}"
    exit 1
fi

# Login to ECR
echo -e "${YELLOW}🔐 Logging in to ECR...${NC}"
aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to login to ECR${NC}"
    exit 1
fi

# Build Docker image for ARM64
echo -e "${YELLOW}🔨 Building ARM64 Docker image...${NC}"
docker build --platform=linux/arm64 -t temp-app:$IMAGE_TAG .
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to build Docker image${NC}"
    exit 1
fi

# Tag image for ECR
echo -e "${YELLOW}🏷️  Tagging image...${NC}"
docker tag temp-app:$IMAGE_TAG $ECR_URL:$IMAGE_TAG

# Push to ECR
echo -e "${YELLOW}⬆️  Pushing image to ECR...${NC}"
docker push $ECR_URL:$IMAGE_TAG
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to push image to ECR${NC}"
    exit 1
fi

# Also tag and push as latest if not already latest
if [ "$IMAGE_TAG" != "latest" ]; then
    echo -e "${YELLOW}🏷️  Tagging as latest...${NC}"
    docker tag temp-app:$IMAGE_TAG $ECR_URL:latest
    docker push $ECR_URL:latest
fi

# Cleanup local images
echo -e "${YELLOW}🧹 Cleaning up local images...${NC}"
docker rmi temp-app:$IMAGE_TAG $ECR_URL:$IMAGE_TAG
[ "$IMAGE_TAG" != "latest" ] && docker rmi $ECR_URL:latest

echo -e "${GREEN}✅ Successfully pushed image to ECR!${NC}"
echo -e "${BLUE}📝 Image URI: $ECR_URL:$IMAGE_TAG${NC}"
echo -e "${BLUE}💡 Next steps:${NC}"
echo -e "   1. Update your Pulumi code to use this image"
echo -e "   2. Run 'pulumi up' to deploy"