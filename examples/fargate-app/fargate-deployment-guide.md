# Fargate 应用部署指南

## 概述

本指南介绍如何在另一个 Pulumi 项目中部署 Fargate 应用，并访问基础设施项目中的 PostgreSQL 和 Redis 资源。

## 架构说明

```mermaid
graph TB
    subgraph AWS["AWS Account"]
        subgraph InfraProject["Infrastructure Project (a1d-pulumi)"]
            VPC["VPC"]
            PostgreSQL["PostgreSQL"]
            Redis["Redis"]
            S3["S3 Bucket"]

            VPC --> PostgreSQL
            VPC --> Redis
            VPC --> S3
        end

        subgraph FargateProject["Fargate Application Project (your-app)"]
            FargateService["Fargate Service (ARM64)<br/>(在相同 VPC 中)"]
        end

        VPC -.-> FargateService
        PostgreSQL -.-> FargateService
        Redis -.-> FargateService
        S3 -.-> FargateService
    end

    style InfraProject fill:#e1f5fe
    style FargateProject fill:#f3e5f5
    style VPC fill:#fff3e0
    style PostgreSQL fill:#e8f5e8
    style Redis fill:#ffebee
    style S3 fill:#fff8e1
    style FargateService fill:#f1f8e9
```

## 步骤 1: 创建新的 Fargate 项目

```bash
mkdir fargate-app
cd fargate-app
pulumi new aws-typescript
```

## 步骤 2: 安装依赖

```bash
pnpm add @pulumi/pulumi @pulumi/aws
```

## 步骤 3: 配置 Pulumi.yaml

```yaml
name: fargate-app
runtime:
  name: nodejs
  options:
    packagemanager: pnpm
    typescript: true
description: Fargate application that uses shared infrastructure
```

## 步骤 4: 设置 Stack Reference

在您的 `src/index.ts` 中使用 Stack Reference 来引用基础设施资源：

```typescript
// 注意: 替换为您的实际组织名和栈名
const infraStack = new pulumi.StackReference("ethan-huo-org/a1d-pulumi/dev");
```

## 步骤 5: 获取基础设施输出

```typescript
const vpcId = infraStack.getOutput("vpcId");
const privateSubnetIds = infraStack.getOutput("privateSubnetIds");
const publicSubnetIds = infraStack.getOutput("publicSubnetIds");
const fargateSecurityGroupId = infraStack.getOutput("fargateSecurityGroupId");
const dbSecretArn = infraStack.getOutput("dbSecretArn");
const postgresEndpoint = infraStack.getOutput("postgresEndpoint");
const redisEndpoint = infraStack.getOutput("redisEndpoint");
```

## 步骤 6: 部署应用

```bash
pulumi up
```

## 网络和安全配置

### 部署流程

```mermaid
flowchart TD
    Start([开始部署]) --> CreateStack[创建 Stack Reference]
    CreateStack --> GetOutputs[获取基础设施输出]

    GetOutputs --> VPC_ID[VPC ID]
    GetOutputs --> Subnets[子网 IDs]
    GetOutputs --> SecurityGroup[安全组 ID]
    GetOutputs --> Secrets[密钥和端点]

    VPC_ID --> DeployFargate[部署 Fargate 服务]
    Subnets --> DeployFargate
    SecurityGroup --> DeployFargate
    Secrets --> DeployFargate

    DeployFargate --> ConfigSG[配置安全组规则]

    ConfigSG --> SGRules{安全组规则}
    SGRules --> HTTP[HTTP/HTTPS<br/>入站规则]
    SGRules --> DB[PostgreSQL<br/>5432 端口]
    SGRules --> Cache[Redis<br/>6379 端口]

    HTTP --> AppAccess[应用访问]
    DB --> AppAccess
    Cache --> AppAccess

    AppAccess --> EnvVars[设置环境变量]
    EnvVars --> DBEndpoint[DB_ENDPOINT]
    EnvVars --> RedisEndpoint[REDIS_ENDPOINT]
    EnvVars --> SecretArn[DB_SECRET_ARN]

    DBEndpoint --> Complete([部署完成])
    RedisEndpoint --> Complete
    SecretArn --> Complete

    style Start fill:#4caf50,color:#fff
    style Complete fill:#4caf50,color:#fff
    style SGRules fill:#ff9800,color:#fff
    style DeployFargate fill:#2196f3,color:#fff
```

### 安全组规则

基础设施项目已经创建了以下安全组规则：

1. **Fargate 安全组** (`fargateSecurityGroupId`):
   - 入站: HTTP (80), HTTPS (443)
   - 出站: 所有流量

2. **数据库访问规则**:
   - 允许 Fargate 安全组访问 PostgreSQL (5432 端口)

3. **缓存访问规则**:
   - 允许 Fargate 安全组访问 Redis (6379 端口)

### 环境变量和密钥

应用可以通过以下方式访问数据库和缓存：

- **环境变量**: `DB_ENDPOINT`, `REDIS_ENDPOINT`
- **AWS Secrets Manager**: 通过 `DB_SECRET_ARN` 获取完整的数据库凭证

## 应用代码示例

### Node.js 应用示例

```javascript
// app.js
const { SecretsManagerClient, GetSecretValueCommand } = require("@aws-sdk/client-secrets-manager");
const redis = require("redis");
const { Pool } = require("pg");

async function getDbCredentials() {
    const client = new SecretsManagerClient({ region: "ap-southeast-2" });
    const response = await client.send(new GetSecretValueCommand({
        SecretId: process.env.DB_SECRET_ARN
    }));
    return JSON.parse(response.SecretString);
}

async function initializeConnections() {
    // 获取数据库凭证
    const dbCredentials = await getDbCredentials();

    // 初始化 PostgreSQL 连接
    const pgPool = new Pool({
        host: dbCredentials.host,
        port: dbCredentials.port,
        database: dbCredentials.dbname,
        user: dbCredentials.username,
        password: dbCredentials.password,
    });

    // 初始化 Redis 连接
    const redisClient = redis.createClient({
        host: process.env.REDIS_ENDPOINT,
        port: 6379,
    });

    return { pgPool, redisClient };
}
```

### 健康检查端点

确保应用有健康检查端点：

```javascript
app.get('/health', (req, res) => {
    res.status(200).json({ status: 'healthy' });
});
```

## 故障排除

### 故障排除流程

```mermaid
flowchart TD
    Problem([遇到问题]) --> CheckType{问题类型}

    CheckType --> DBConnection[无法连接数据库]
    CheckType --> RedisConnection[无法访问 Redis]
    CheckType --> SecretAccess[无法获取密钥]

    DBConnection --> CheckSG1[检查安全组规则]
    CheckSG1 --> CheckSubnet1[确认 Fargate 在正确子网]
    CheckSubnet1 --> TestDB[测试数据库连接]
    TestDB --> DBFixed([数据库问题解决])

    RedisConnection --> CheckVPC[检查 VPC 内部访问]
    CheckVPC --> CheckSG2[检查 Redis 安全组]
    CheckSG2 --> TestRedis[测试 Redis 连接]
    TestRedis --> RedisFixed([Redis 问题解决])

    SecretAccess --> CheckRole[确认 Task Role 权限]
    CheckRole --> CheckArn[检查 Secret ARN]
    CheckArn --> TestSecret[测试密钥获取]
    TestSecret --> SecretFixed([密钥问题解决])

    DBFixed --> Monitor[监控应用状态]
    RedisFixed --> Monitor
    SecretFixed --> Monitor

    Monitor --> Logs[查看 CloudWatch 日志]
    Logs --> Metrics[检查性能指标]
    Metrics --> Success([问题解决])

    style Problem fill:#f44336,color:#fff
    style Success fill:#4caf50,color:#fff
    style CheckType fill:#ff9800,color:#fff
    style DBFixed fill:#4caf50,color:#fff
    style RedisFixed fill:#4caf50,color:#fff
    style SecretFixed fill:#4caf50,color:#fff
```

### 常见问题

1. **无法连接到数据库**:
   - 检查安全组规则是否正确
   - 确认 Fargate 任务在正确的子网中

2. **无法访问 Redis**:
   - Redis 在私有子网中，需要通过 VPC 内部访问
   - 检查 Redis 安全组规则

3. **无法获取密钥**:
   - 确认 Task Role 有 Secrets Manager 权限
   - 检查 Secret ARN 是否正确

### 调试命令

```bash
# 查看 ECS 服务状态
aws ecs describe-services --cluster fargate-app-cluster --services app-service

# 查看任务日志
aws logs tail /ecs/fargate-app --follow

# 测试数据库连接
aws rds describe-db-instances --db-instance-identifier postgres-db
```

## 成本优化

- 使用最小的 Fargate 规格 (256 CPU, 512 Memory)
- 设置适当的日志保留期 (7 天)
- 考虑使用 Spot 容量 (如果应用允许中断)

## 安全最佳实践

1. 使用 Secrets Manager 存储敏感信息
2. 最小权限原则配置 IAM 角色
3. 启用 CloudWatch 日志记录
4. 定期轮换数据库密码
5. 使用 HTTPS 和适当的安全组规则