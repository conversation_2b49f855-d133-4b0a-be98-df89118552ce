{"name": "fargate-app", "version": "1.0.0", "description": "Fargate application with shared infrastructure", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "bun run src/index.ts", "docker:build": "docker build -t fargate-app .", "docker:push": "./scripts/build-and-push.sh", "docker:push:tag": "./scripts/build-and-push.sh", "deploy:build": "pnpm build && pnpm run docker:push", "deploy": "pnpm run deploy:build && pulumi up", "deploy:prod": "pnpm build && ./scripts/build-and-push.sh $(git rev-parse --short HEAD) && pulumi up --stack production"}, "dependencies": {"@aws-sdk/client-secrets-manager": "^3.0.0", "express": "^4.18.0", "pg": "^8.11.0", "redis": "^4.6.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/node": "^20.0.0", "@types/pg": "^8.10.0", "@pulumi/aws": "^6.0.0", "@pulumi/pulumi": "^3.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "packageManager": "pnpm@10.12.4"}