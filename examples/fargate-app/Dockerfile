# Multi-stage build for Node.js application with pnpm on ARM64
FROM --platform=linux/arm64 node:20-alpine AS base

# Install curl for health checks and enable pnpm
RUN apk add --no-cache curl && corepack enable

WORKDIR /app

FROM base AS deps
# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install production dependencies with cache mount
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm fetch --frozen-lockfile

RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm install --frozen-lockfile --prod

FROM base AS build
# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install all dependencies (including dev) for building
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm fetch --frozen-lockfile

RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm install --frozen-lockfile

# Copy source code and build
COPY . .
RUN pnpm build

# Production stage
FROM --platform=linux/arm64 node:20-alpine AS production

# Install curl for health checks and enable pnpm
RUN apk add --no-cache curl && corepack enable

WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Copy dependencies from deps stage and built application from build stage
COPY --from=deps --chown=nodejs:nodejs /app/node_modules ./node_modules
COPY --from=build --chown=nodejs:nodejs /app/dist ./dist
COPY --chown=nodejs:nodejs package.json pnpm-lock.yaml ./

# Switch to non-root user
USER nodejs

# Expose application port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["node", "dist/index.js"]