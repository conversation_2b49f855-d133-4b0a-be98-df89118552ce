# Azure Backend App 部署指南

## 概述

这个项目展示了如何将基于 Bun 的 TypeScript API 服务部署到 Azure Container Apps，实现：

- 🐳 基于 Bun 镜像的容器化部署
- 🏗️ 使用 Pulumi 的基础设施即代码
- 🔄 GitHub Actions 自动化 CI/CD
- 🌐 公网访问和自动扩缩容
- 🔗 与共享基础设施资源的集成

## ⚠️ 重要注意事项和踩坑经验

### 🐳 Docker 镜像构建

**问题**: 直接使用 `docker push` 推送到 ACR 经常遇到认证问题

**解决方案**: 使用 Azure CLI 在云端构建
```bash
# ❌ 避免使用 docker push（认证复杂）
docker build -t myregistry.azurecr.io/myapp:latest .
docker push myregistry.azurecr.io/myapp:latest

# ✅ 推荐使用 ACR 云端构建
az acr build --registry myregistry --image myapp:latest .
```

### 🔧 Dockerfile 配置要点

**关键修复点**:

1. **基础镜像**: 移除 `--platform` 参数
```dockerfile
# ❌ ACR 不支持平台参数
FROM --platform=linux/arm64 oven/bun:latest AS base

# ✅ 使用默认平台
FROM oven/bun:latest AS base
```

2. **包管理器**: Bun 镜像基于 Debian，使用 apt-get
```dockerfile
# ❌ Alpine 命令
RUN apk add --no-cache curl

# ✅ Debian 命令
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*
```

3. **用户创建**: 使用 Debian 语法
```dockerfile
# ❌ Alpine 语法
RUN addgroup -g 1001 -S bunuser && adduser -S bunuser -u 1001

# ✅ Debian 语法
RUN groupadd -g 1001 bunuser && useradd -r -u 1001 -g bunuser bunuser
```

### 🌐 网络配置（最关键！）

**问题**: 容器内应用无法从外部访问

**解决方案**: Bun.serve 必须监听 0.0.0.0
```typescript
// ❌ 默认只监听 localhost，容器外无法访问
Bun.serve({
    port: 3000,
    // ...
});

// ✅ 监听所有接口，允许容器外访问
Bun.serve({
    port: 3000,
    hostname: "0.0.0.0", // 关键配置！
    // ...
});
```

### 🔐 ACR 认证配置

**推荐方案**: 使用管理员凭据而非托管身份
```typescript
// ✅ 在 Pulumi 中配置 ACR 认证
const acrCredentials = resourceGroupName.apply(rgName =>
    azure.containerregistry.listRegistryCredentialsOutput({
        resourceGroupName: rgName,
        registryName: acrName,
    })
);

// Container App 配置
registries: [{
    server: acrLoginServer,
    username: acrCredentials.apply(creds => creds.username || ""),
    passwordSecretRef: "acr-password",
}],
secrets: [{
    name: "acr-password",
    value: acrCredentials.apply(creds => creds.passwords?.[0]?.value || ""),
}],
```

### 🔗 Pulumi Stack 引用

**问题**: Stack 引用路径错误导致无法找到共享资源

**解决方案**: 使用正确的组织/项目/栈格式
```yaml
# ❌ 错误格式
config:
  azure-backend-app:infraStackRef: "a1d/a1d-azure/dev"

# ✅ 正确格式：organization/project/stack
config:
  azure-backend-app:infraStackRef: "ethan-huo-org/a1d-pulumi-azure/azure-dev"
```

**验证方法**:
```bash
# 查看当前组织
pulumi org get-default

# 查看 stack 列表
cd cloud/azure && pulumi stack ls
```

### 📝 部署顺序

**重要**: 必须先部署基础设施，再部署应用

```bash
# 1. 先部署共享基础设施
cd cloud/azure
pulumi up

# 2. 再部署应用
cd ../../examples/azure-backend-app
pulumi stack init dev  # 首次部署需要初始化
pulumi up
```

### 🔄 镜像更新流程

当应用代码更新后，需要重新构建和部署：

```bash
# 1. 重新构建镜像
az acr build --registry a1dazureacr --image azure-backend-app:latest .

# 2. 触发 Pulumi 更新（会自动创建新 revision）
pulumi up --yes
```

### 🩺 故障排查

**查看应用状态**:
```bash
# 检查 Container App 状态
az containerapp show --name backend-api-app --resource-group <resource-group> \
    --query "{name:name,status:properties.provisioningState,fqdn:properties.configuration.ingress.fqdn}"

# 查看应用日志
az containerapp logs show --name backend-api-app --resource-group <resource-group> --tail=20

# 测试健康检查
curl https://<app-url>/health
```

## 🚀 快速部署检查清单

在开始部署前，请确认以下要点：

### ✅ 基础环境检查
- [ ] Azure CLI 已登录 (`az account show`)
- [ ] Pulumi CLI 已安装并登录
- [ ] 基础设施已部署 (`cd cloud/azure && pulumi stack ls`)

### ✅ Dockerfile 检查
- [ ] 移除了 `--platform` 参数
- [ ] 使用 `apt-get` 而不是 `apk`
- [ ] 用户创建使用 `groupadd` 和 `useradd`

### ✅ 应用代码检查
- [ ] Bun.serve 包含 `hostname: "0.0.0.0"`
- [ ] 健康检查端点 `/health` 已实现

### ✅ Pulumi 配置检查
- [ ] Stack 引用路径格式正确 (`organization/project/stack`)
- [ ] ACR 认证使用管理员凭据
- [ ] 环境变量正确配置

### ✅ 部署流程
1. [ ] 构建镜像: `az acr build --registry <registry> --image <image>:latest .`
2. [ ] 初始化 stack: `pulumi stack init dev` (首次部署)
3. [ ] 部署应用: `pulumi up`
4. [ ] 验证健康检查: `curl https://<app-url>/health`

## 架构设计

### 分离式架构

```mermaid
flowchart TD
    subgraph SharedInfra ["共享基础设施层<br/>cloud/azure/src/index.ts"]
        RG["Resource Group"]
        VNet["Virtual Network"]
        PG["PostgreSQL Database"]
        Redis["Redis Cache"]
        ACR["Container Registry (ACR)"]
        CAE["Container Apps Environment"]
        Storage["Storage Account + CDN"]
    end

    subgraph AppLayer ["应用部署层<br/>examples/azure-backend-app/"]
        Dockerfile["Dockerfile (Bun ARM64)"]
        Pulumi["Pulumi 配置<br/>(引用基础设施)"]
        GHA["GitHub Actions (CI/CD)"]
        ContainerApp["Container App<br/>(公网访问)"]
    end

    SharedInfra --> AppLayer

    style SharedInfra fill:#e1f5fe
    style AppLayer fill:#f3e5f5
```

### 实际场景映射

在真实项目中：
- **基础设施仓库**: `cloud/azure/` → 独立的基础设施仓库
- **应用仓库**: `examples/azure-backend-app/` → 各个应用的独立仓库
- **资源共享**: 通过 Pulumi Stack Reference 实现跨仓库资源引用

## 部署流程

### 1. 基础设施部署（一次性）

```bash
# 在 cloud/azure/ 目录下
cd cloud/azure
pulumi up --stack dev
```

这会创建所有共享资源，包括：
- Azure Container Registry
- Container Apps Environment
- 数据库、缓存等基础服务
- 网络和安全配置

### 2. 应用部署

#### 方式一：自动化部署（推荐）

推送代码到 GitHub 会自动触发：

```yaml
# .github/workflows/deploy.yml
on:
  push:
    branches: [main, develop]
    paths: ['examples/azure-backend-app/**']
```

部署流程：
1. 构建 ARM64 容器镜像
2. 推送到 Azure Container Registry
3. 使用 Pulumi 部署 Container App
4. 执行健康检查

#### 方式二：本地部署

```bash
cd examples/azure-backend-app

# 一键部署
./scripts/deploy.sh

# 或分步执行
./scripts/deploy.sh build   # 构建镜像
./scripts/deploy.sh push    # 推送镜像
./scripts/deploy.sh deploy  # 部署应用
./scripts/deploy.sh health  # 健康检查
```

#### 方式三：手动部署

```bash
# 1. 构建镜像
docker build --platform linux/arm64 -t azure-backend-app .

# 2. 标记并推送
az acr login --name a1dazureacr
docker tag azure-backend-app a1dazureacr.azurecr.io/azure-backend-app:latest
docker push a1dazureacr.azurecr.io/azure-backend-app:latest

# 3. 部署应用
bun install
pulumi up --stack dev
```

## 配置说明

### Stack Reference 配置

应用通过 Stack Reference 引用基础设施资源：

```yaml
# Pulumi.yaml
config:
  azure-backend-app:infraStackRef:
    default: "a1d/a1d-azure/dev"
```

### 环境变量自动注入

应用会自动获取基础设施导出的环境变量：

```typescript
// index.ts
const envConfig = infraStack.getOutput("envConfig");
const environmentVars = envConfig.apply((config: string) => {
  const parsed = JSON.parse(config);
  return Object.entries(parsed).map(([name, value]) => ({
    name,
    value: String(value),
  }));
});
```

包括：
- `DATABASE_URL`: PostgreSQL 连接字符串
- `REDIS_URL`: Redis 连接字符串
- `AZURE_STORAGE_*`: 存储账户配置
- `CDN_URL`: CDN 端点
- 其他基础设施信息

### 多环境支持

```bash
# 开发环境
pulumi up --stack dev

# 生产环境
pulumi up --stack prod
```

不同环境使用不同的资源配置：
- `Pulumi.dev.yaml`: 开发环境（1 副本，0.5 CPU）
- `Pulumi.prod.yaml`: 生产环境（2 副本，1.0 CPU）

## 容器配置

### Dockerfile 特点

```dockerfile
# 基于 Bun ARM64 镜像
FROM --platform=linux/arm64 oven/bun:1.1.38-alpine

# 无编译过程，直接运行 TypeScript
CMD ["bun", "run", "src/index.ts"]
```

优势：
- ARM64 架构，性能更好，成本更低
- Bun 原生支持 TypeScript，无需编译
- 镜像体积小，启动速度快

### 健康检查

```typescript
// src/index.ts
if (url.pathname === "/health") {
  return new Response(JSON.stringify({
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  }));
}
```

Container Apps 会自动使用此端点进行：
- Liveness Probe（存活检查）
- Readiness Probe（就绪检查）

## 网络和安全

### 公网访问

Container App 配置为外部访问：

```typescript
ingress: {
  external: true,
  targetPort: 3000,
  transport: azure.app.IngressTransportMethod.Http,
}
```

自动获得：
- HTTPS 证书
- 负载均衡
- 自定义域名支持

### 安全配置

- 非 root 用户运行容器
- 系统分配的托管身份
- 网络安全组保护
- Key Vault 密钥管理

## 扩缩容策略

```typescript
scale: {
  minReplicas: 1,
  maxReplicas: 10,
  rules: [{
    name: "http-scaling",
    http: {
      metadata: {
        concurrentRequests: "100",
      },
    },
  }],
}
```

- **触发条件**: 并发请求数 > 100
- **扩容策略**: 自动增加副本
- **缩容策略**: 流量减少时自动缩容

## 监控和日志

### 内置监控

- Azure Monitor 集成
- Log Analytics 工作区
- 容器指标收集
- 应用性能监控

### 日志查看

```bash
# 查看应用日志
az containerapp logs show \
  --name backend-api-app \
  --resource-group a1d-azure-rg

# 查看 Pulumi 日志
pulumi logs --stack dev
```

## 测试验证

### 自动化测试

```bash
# 运行完整测试套件
./scripts/test.sh

# 单独测试
./scripts/test.sh health  # 健康检查
./scripts/test.sh rpc     # RPC 端点
./scripts/test.sh load    # 负载测试
```

### 手动测试

```bash
# 获取应用 URL
APP_URL=$(pulumi stack output appUrl --stack dev)

# 健康检查
curl $APP_URL/health

# RPC 调用
curl -X POST $APP_URL/rpc \
  -H "Content-Type: application/json" \
  -d '{"method": "ping", "params": []}'
```

## 故障排除

### 常见问题

1. **镜像推送失败**
   ```bash
   az acr login --name a1dazureacr
   ```

2. **Stack Reference 错误**
   ```bash
   # 确认基础设施栈存在
   pulumi stack ls --organization a1d
   ```

3. **容器启动失败**
   ```bash
   # 查看容器日志
   az containerapp logs show --name backend-api-app --resource-group a1d-azure-rg
   ```

### 调试技巧

- 使用 `pulumi preview` 预览变更
- 检查 Container Apps 事件日志
- 验证环境变量注入
- 测试健康检查端点

## 成本优化

- ARM64 架构降低计算成本
- 自动扩缩容避免资源浪费
- 共享基础设施降低整体成本
- 按需付费的 Container Apps

## 下一步

1. 添加更多 API 端点
2. 集成数据库和缓存
3. 添加认证和授权
4. 配置自定义域名
5. 设置监控告警
