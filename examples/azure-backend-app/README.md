# Azure Backend App

基于 Bun 运行时的 Azure Container Apps 后端应用示例，展示如何在 Azure 上部署容器化的 API 服务。

## 功能特性

- 🚀 基于 Bun 运行时的高性能 TypeScript 应用
- 🐳 容器化部署，优化性能和成本
- 🔄 自动化 CI/CD 流水线（GitHub Actions）
- 🏗️ 基础设施即代码（Pulumi）
- 🌐 公网访问和自动扩缩容
- 📊 健康检查和监控
- 🔐 与 Azure 基础设施资源集成

## 📚 文档导航

- **[DEPLOYMENT.md](./DEPLOYMENT.md)** - 详细部署指南和配置要点
- **[TROUBLESHOOTING.md](./TROUBLESHOOTING.md)** - 常见问题和故障排查
- **[../docs/fullstack-deployment-guide.md](../docs/fullstack-deployment-guide.md)** - 全栈部署流程

## ⚠️ 快速提醒

首次部署请务必阅读 `DEPLOYMENT.md` 中的"踩坑经验"部分，可以避免常见问题：

- **网络配置**: Bun.serve 必须监听 `0.0.0.0`
- **镜像推送**: 使用 `az acr build` 而不是 `docker push`
- **Dockerfile**: 注意 Debian 与 Alpine 语法差异
- **Stack 引用**: 确保使用正确的组织/项目/栈格式

## 架构概览

```mermaid
flowchart TD
    GH["GitHub Repo"] --> GHA["GitHub Actions"]
    GHA --> ACR["Azure Container<br/>Registry"]
    GHA --> PD["Pulumi Deploy"]

    ACR --> CAE["Container Apps<br/>Environment"]
    PD --> CAE

    CAE --> API["Backend API<br/>(Public URL)"]

    style GH fill:#f9f9f9
    style GHA fill:#e3f2fd
    style ACR fill:#fff3e0
    style PD fill:#e8f5e8
    style CAE fill:#fce4ec
    style API fill:#f3e5f5
```

## 快速开始

### 前置要求

- [Bun](https://bun.sh/) >= 1.1.38
- [Docker](https://docker.com/)
- [Azure CLI](https://docs.microsoft.com/en-us/cli/azure/)
- [Pulumi CLI](https://www.pulumi.com/docs/get-started/install/)

### 本地开发

1. 安装依赖：
```bash
bun install
```

2. 启动开发服务器：
```bash
bun run dev
```

3. 访问应用：
- API 端点: http://localhost:3000/rpc
- 健康检查: http://localhost:3000/health

### 部署到 Azure

#### 方法 1: 使用部署脚本（推荐）

```bash
# 构建并部署到开发环境
./scripts/deploy.sh

# 部署到生产环境
STACK_NAME=prod ./scripts/deploy.sh
```

#### 方法 2: 手动部署

1. 构建容器镜像：
```bash
bun run build:docker
```

2. 推送到 Azure Container Registry：
```bash
az acr login --name a1dazureacr
docker tag azure-backend-app a1dazureacr.azurecr.io/azure-backend-app:latest
docker push a1dazureacr.azurecr.io/azure-backend-app:latest
```

3. 部署应用：
```bash
bun run deploy
```

#### 方法 3: GitHub Actions（自动化）

推送代码到 `main` 或 `develop` 分支会自动触发部署流水线。

## 配置说明

### Pulumi 配置

主要配置项在 `Pulumi.yaml` 中定义：

- `infraStackRef`: 基础设施栈引用（默认: `a1d/a1d-azure/dev`）
- `containerImage`: 容器镜像地址
- `replicas`: 副本数量
- `cpu`: CPU 分配（核心数）
- `memory`: 内存分配（GB）
- `port`: 应用端口

### 环境变量

应用会自动从基础设施栈获取以下环境变量：

- 数据库连接信息（PostgreSQL）
- Redis 缓存配置
- 存储账户信息
- CDN 配置
- 其他 Azure 资源信息

## API 端点

### 健康检查
```
GET /health
```

响应示例：
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.456
}
```

### RPC 端点
```
POST /rpc
```

支持的 RPC 方法：
- `ping`: 返回 ping 消息
- `pong`: 返回 pong 消息
- `nested.ping`: 嵌套的 ping 方法
- `nested.pong`: 嵌套的 pong 方法

## 监控和日志

- **健康检查**: `/health` 端点提供应用状态
- **Azure Monitor**: 自动集成 Azure 监控和日志分析
- **容器日志**: 通过 Azure Container Apps 查看应用日志
- **指标监控**: CPU、内存、请求数等指标自动收集

## 扩缩容配置

应用支持基于 HTTP 请求的自动扩缩容：

- **最小副本数**: 1
- **最大副本数**: 10（或副本数的3倍）
- **扩容触发**: 并发请求数 > 100
- **缩容策略**: 自动缩容到最小副本数

## 安全配置

- 使用非 root 用户运行容器
- 系统分配的托管身份
- HTTPS 强制访问
- 网络安全组保护
- Key Vault 密钥管理

## 故障排除

### 常见问题

1. **容器启动失败**
   - 检查健康检查端点是否正常
   - 查看容器日志确认错误信息

2. **无法访问数据库**
   - 确认基础设施栈已正确部署
   - 检查网络安全组配置

3. **镜像推送失败**
   - 确认已登录到 Azure Container Registry
   - 检查镜像标签是否正确

### 查看日志

```bash
# 查看 Pulumi 部署日志
pulumi logs --stack dev

# 查看 Azure Container Apps 日志
az containerapp logs show --name backend-api-app --resource-group a1d-azure-rg
```

## 开发指南

### 添加新的 API 端点

1. 在 `src/router.ts` 中添加新的处理器
2. 更新类型定义
3. 添加相应的测试

### 修改容器配置

1. 更新 `Dockerfile`
2. 修改 `index.ts` 中的容器配置
3. 重新部署应用

## 相关资源

- [Azure Container Apps 文档](https://docs.microsoft.com/en-us/azure/container-apps/)
- [Pulumi Azure Native 文档](https://www.pulumi.com/registry/packages/azure-native/)
- [Bun 文档](https://bun.sh/docs)
- [ORPC 文档](https://orpc.unnoq.com/)

## 许可证

MIT License
