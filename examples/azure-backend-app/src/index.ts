import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@orpc/server/fetch";
import { CORSPlugin } from "@orpc/server/plugins";
import { router } from "./router";

const handler = new RPCHandler(router, {
	plugins: [new CORSPlugin()],
});

const port = process.env.PORT || 3000;

console.log(`🚀 Server running at http://0.0.0.0:${port}`);

Bun.serve({
	port,
	hostname: "0.0.0.0",
	async fetch(request: Request) {
		const url = new URL(request.url);

		// Health check endpoint for container health monitoring
		if (url.pathname === "/health") {
			return new Response(
				JSON.stringify({
					status: "healthy",
					timestamp: new Date().toISOString(),
					uptime: process.uptime(),
				}),
				{
					status: 200,
					headers: { "Content-Type": "application/json" },
				}
			);
		}

		const { matched, response } = await handler.handle(request, {
			prefix: "/rpc",
			context: {}, // Provide initial context if needed
		});

		if (matched) {
			return response;
		}

		return new Response("Not found", { status: 404 });
	},
});
