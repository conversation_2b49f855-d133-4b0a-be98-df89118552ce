import { RPCLink } from "@orpc/client/fetch";
import {
	ClientRetryPlugin,
	ClientRetryPluginContext,
} from "@orpc/client/plugins";
import type { AppRouter } from "./router";
import { createORPCClient } from "@orpc/client";
import type { RouterClient } from "@orpc/server";
import { inspect } from "bun";

interface ORPCClientContext extends ClientRetryPluginContext {}

const link = new RPCLink<ORPCClientContext>({
	url: "http://localhost:3000/rpc",
	plugins: [
		new ClientRetryPlugin({
			default: {
				// Optional override for default options
				retry: ({ path }) => {
					if (path.join(".") === "planet.list") {
						return 2;
					}

					return 0;
				},
			},
		}),
	],
});

const client: RouterClient<AppRouter, ORPCClientContext> =
	createORPCClient(link);

if (import.meta.main) {
	console.log("=".repeat(80));
	const res = await client.ping();
	console.log(inspect(res, { depth: Infinity }));
	console.log("=".repeat(80));

	const stream = await client.chat({
		chatId: "1",
		messages: [
			{
				id: "1",
				role: "user",
				parts: [{ type: "text", text: "Hello, how are you?" }],
			},
		],
	});

	const enableChat = process.argv.includes('--enable-chat') || process.env.ENABLE_CHAT === 'true';
	if (enableChat) {
		for await (const chunk of stream) {
			switch (chunk.type) {
				case "text-delta":
					process.stdout.write(chunk.delta);
					break;
				case "reasoning-start": {
					process.stdout.write("\n<think>\n");
					break;
				}
				case "reasoning-delta": {
					process.stdout.write(chunk.delta);
					break;
				}
				case "reasoning-end": {
					process.stdout.write("\n</think>\n");
					break;
				}
				default: {
					console.log(chunk);
					break;
				}
			}
		}
	}
}
