import { type } from "arktype";

export const runtimeEnvSchema = type({
	// API Keys
	GROQ_API_KEY: "string",

	// Server Configuration
	PORT: "string",

	// Database Configuration
	DATABASE_URL: "string",

	// Redis Configuration
	REDIS_URL: "string",

	// Storage Configuration
	AZURE_STORAGE_CONNECTION_STRING: "string",
	CDN_URL: "string",
});

export type RuntimeEnv = typeof runtimeEnvSchema.infer;

declare global {
	namespace NodeJS {
		interface ProcessEnv extends RuntimeEnv {}
	}
}
