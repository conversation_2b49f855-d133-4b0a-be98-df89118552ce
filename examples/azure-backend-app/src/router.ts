import { os, streamToEventIterator, type } from "@orpc/server";

import { createGroq } from "@ai-sdk/groq";

const groq = createGroq({
	apiKey: process.env.GROQ_API_KEY,
});

import { convertToModelMessages, streamText, UIMessage } from "ai";

const ping = os.$route({ method: "GET" }).handler(async () => ({
	status: "UP",
	timestamp: new Date().toISOString(),
}));

const chat = os
	.input(type<{ chatId: string; messages: UIMessage[] }>())
	.handler(async ({ input }) => {
		const result = streamText({
			model: groq("moonshotai/kimi-k2-instruct"),
			messages: convertToModelMessages(input.messages),
		});

		return streamToEventIterator(result.toUIMessageStream());
	});

export const router = {
	ping,
	chat,
};

export type AppRouter = typeof router;
