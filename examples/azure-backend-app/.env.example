# Azure Backend App Environment Variables
# Copy this file to .env and fill in the values

# Application Configuration
PORT=3000
NODE_ENV=development

# Database Configuration (from Azure infrastructure)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/maindb?sslmode=require
DB_HOST=your-postgres-server.postgres.database.azure.com
DB_PORT=5432
DB_NAME=maindb
DB_USER=postgres
DB_PASSWORD=your-db-password

# Redis Configuration (from Azure infrastructure)
REDIS_URL=rediss://:<EMAIL>:6380
REDIS_HOST=your-redis-cache.redis.cache.windows.net
REDIS_PORT=6380
REDIS_KEY=your-redis-primary-key

# Storage Configuration (from Azure infrastructure)
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=yourstorageaccount;AccountKey=your-storage-key;EndpointSuffix=core.windows.net
AZURE_STORAGE_ACCOUNT_NAME=yourstorageaccount
AZURE_STORAGE_CONTAINER_NAME=static

# CDN Configuration (from Azure infrastructure)
CDN_URL=https://your-cdn-endpoint.azureedge.net

# Container Registry (from Azure infrastructure)
ACR_LOGIN_SERVER=yourregistry.azurecr.io

# Key Vault Configuration (from Azure infrastructure)
KEY_VAULT_URI=https://your-keyvault.vault.azure.net/

# Azure Resource Information (from Azure infrastructure)
AZURE_RESOURCE_GROUP=your-resource-group
AZURE_LOCATION=East Asia

# Pulumi Configuration
PULUMI_ACCESS_TOKEN=your-pulumi-token
PULUMI_CONFIG_PASSPHRASE=your-passphrase

# Azure Credentials for CI/CD
AZURE_CREDENTIALS={"clientId":"...","clientSecret":"...","subscriptionId":"...","tenantId":"..."}
ACR_USERNAME=your-acr-username
ACR_PASSWORD=your-acr-password
