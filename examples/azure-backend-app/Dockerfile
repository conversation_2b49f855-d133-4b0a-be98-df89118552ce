# Bun runtime for Azure Container Apps
FROM oven/bun:latest AS base

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Create non-root user for security
RUN groupadd -g 1001 bunuser && \
    useradd -r -u 1001 -g bunuser bunuser

# Copy package files first for better caching
COPY --chown=bunuser:bunuser package.json ./

# Install dependencies
RUN bun install --production --frozen-lockfile

# Copy source code
COPY --chown=bunuser:bunuser src ./src

# Switch to non-root user
USER bunuser

# Expose application port
EXPOSE 3000

# Health check endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start application with Bun
CMD ["bun", "run", "src/index.ts"]
