#!/bin/bash

# Azure Backend App Deployment Script
# This script builds and deploys the Azure backend app

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
IMAGE_NAME="azure-backend-app"
REGISTRY="a1dazureacr.azurecr.io"
STACK_NAME="${STACK_NAME:-dev}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Pulumi is installed
    if ! command -v pulumi &> /dev/null; then
        log_error "Pulumi is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Bun is installed
    if ! command -v bun &> /dev/null; then
        log_error "Bun is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Azure CLI is installed and logged in
    if ! command -v az &> /dev/null; then
        log_error "Azure CLI is not installed or not in PATH"
        exit 1
    fi
    
    if ! az account show &> /dev/null; then
        log_error "Not logged in to Azure. Please run 'az login'"
        exit 1
    fi
    
    log_success "All prerequisites met"
}

# Build Docker image
build_image() {
    log_info "Building Docker image..."
    
    cd "$PROJECT_DIR"
    
    # Generate image tag
    GIT_SHA=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    IMAGE_TAG="${REGISTRY}/${IMAGE_NAME}:${GIT_SHA}"
    LATEST_TAG="${REGISTRY}/${IMAGE_NAME}:latest"
    
    # Build image
    docker build \
        --platform linux/arm64 \
        -t "$IMAGE_TAG" \
        -t "$LATEST_TAG" \
        .
    
    log_success "Image built: $IMAGE_TAG"
    echo "$IMAGE_TAG" > .image-tag
}

# Push Docker image
push_image() {
    log_info "Pushing Docker image to registry..."
    
    IMAGE_TAG=$(cat .image-tag)
    LATEST_TAG="${REGISTRY}/${IMAGE_NAME}:latest"
    
    # Login to ACR
    az acr login --name "${REGISTRY%%.*}"
    
    # Push images
    docker push "$IMAGE_TAG"
    docker push "$LATEST_TAG"
    
    log_success "Image pushed: $IMAGE_TAG"
}

# Deploy with Pulumi
deploy_app() {
    log_info "Deploying application with Pulumi..."
    
    cd "$PROJECT_DIR"
    
    # Install dependencies
    bun install
    
    # Get image tag
    IMAGE_TAG=$(cat .image-tag)
    
    # Set Pulumi configuration
    pulumi config set azure-backend-app:containerImage "$IMAGE_TAG" --stack "$STACK_NAME"
    pulumi config set azure-backend-app:infraStackRef "a1d/a1d-azure/$STACK_NAME" --stack "$STACK_NAME"
    
    # Deploy
    pulumi up --yes --stack "$STACK_NAME"
    
    # Get outputs
    APP_URL=$(pulumi stack output appUrl --stack "$STACK_NAME" 2>/dev/null || echo "")
    
    if [ -n "$APP_URL" ]; then
        log_success "Application deployed successfully!"
        log_info "Application URL: $APP_URL"
        log_info "Health check: $APP_URL/health"
        log_info "RPC endpoint: $APP_URL/rpc"
    else
        log_warning "Deployment completed but could not retrieve application URL"
    fi
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    APP_URL=$(pulumi stack output appUrl --stack "$STACK_NAME" 2>/dev/null || echo "")
    
    if [ -z "$APP_URL" ]; then
        log_warning "Could not retrieve application URL for health check"
        return
    fi
    
    HEALTH_URL="$APP_URL/health"
    
    # Wait for app to be ready
    log_info "Waiting for application to be ready..."
    for i in {1..30}; do
        if curl -f -s "$HEALTH_URL" > /dev/null; then
            log_success "Health check passed!"
            curl -s "$HEALTH_URL" | jq .
            return
        fi
        log_info "Attempt $i/30: Waiting for application..."
        sleep 10
    done
    
    log_error "Health check failed after 5 minutes"
    exit 1
}

# Main deployment flow
main() {
    log_info "Starting Azure Backend App deployment..."
    log_info "Stack: $STACK_NAME"
    log_info "Registry: $REGISTRY"
    
    check_prerequisites
    build_image
    push_image
    deploy_app
    health_check
    
    log_success "Deployment completed successfully!"
}

# Handle script arguments
case "${1:-}" in
    "build")
        check_prerequisites
        build_image
        ;;
    "push")
        push_image
        ;;
    "deploy")
        deploy_app
        ;;
    "health")
        health_check
        ;;
    *)
        main
        ;;
esac
