#!/bin/bash

# Azure Backend App Testing Script
# This script tests the deployed application

set -e

# Configuration
STACK_NAME="${STACK_NAME:-dev}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get application URL from Pulumi
get_app_url() {
    cd "$PROJECT_DIR"
    APP_URL=$(pulumi stack output appUrl --stack "$STACK_NAME" 2>/dev/null || echo "")
    
    if [ -z "$APP_URL" ]; then
        log_error "Could not retrieve application URL from Pulumi stack: $STACK_NAME"
        exit 1
    fi
    
    echo "$APP_URL"
}

# Test health endpoint
test_health() {
    local app_url="$1"
    local health_url="$app_url/health"
    
    log_info "Testing health endpoint: $health_url"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$health_url")
    http_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        log_success "Health check passed"
        echo "$body" | jq . 2>/dev/null || echo "$body"
        return 0
    else
        log_error "Health check failed with HTTP $http_code"
        echo "$body"
        return 1
    fi
}

# Test RPC endpoints
test_rpc() {
    local app_url="$1"
    local rpc_url="$app_url/rpc"
    
    log_info "Testing RPC endpoints: $rpc_url"
    
    # Test ping method
    log_info "Testing ping method..."
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"method": "ping", "params": []}' \
        "$rpc_url")
    
    http_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        log_success "Ping method test passed"
        echo "$body" | jq . 2>/dev/null || echo "$body"
    else
        log_error "Ping method test failed with HTTP $http_code"
        echo "$body"
        return 1
    fi
    
    # Test pong method
    log_info "Testing pong method..."
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"method": "pong", "params": []}' \
        "$rpc_url")
    
    http_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        log_success "Pong method test passed"
        echo "$body" | jq . 2>/dev/null || echo "$body"
    else
        log_error "Pong method test failed with HTTP $http_code"
        echo "$body"
        return 1
    fi
    
    # Test nested ping method
    log_info "Testing nested.ping method..."
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"method": "nested.ping", "params": []}' \
        "$rpc_url")
    
    http_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        log_success "Nested ping method test passed"
        echo "$body" | jq . 2>/dev/null || echo "$body"
    else
        log_error "Nested ping method test failed with HTTP $http_code"
        echo "$body"
        return 1
    fi
}

# Load testing
load_test() {
    local app_url="$1"
    local health_url="$app_url/health"
    
    log_info "Running basic load test..."
    
    # Simple load test with 10 concurrent requests
    for i in {1..10}; do
        curl -s "$health_url" > /dev/null &
    done
    
    wait
    log_success "Load test completed"
}

# Main testing flow
main() {
    log_info "Starting Azure Backend App testing..."
    log_info "Stack: $STACK_NAME"
    
    # Get application URL
    APP_URL=$(get_app_url)
    log_info "Application URL: $APP_URL"
    
    # Run tests
    test_health "$APP_URL"
    test_rpc "$APP_URL"
    load_test "$APP_URL"
    
    log_success "All tests passed!"
    
    # Display summary
    echo ""
    log_info "=== Test Summary ==="
    log_info "Application URL: $APP_URL"
    log_info "Health endpoint: $APP_URL/health"
    log_info "RPC endpoint: $APP_URL/rpc"
    log_info "Stack: $STACK_NAME"
}

# Handle script arguments
case "${1:-}" in
    "health")
        APP_URL=$(get_app_url)
        test_health "$APP_URL"
        ;;
    "rpc")
        APP_URL=$(get_app_url)
        test_rpc "$APP_URL"
        ;;
    "load")
        APP_URL=$(get_app_url)
        load_test "$APP_URL"
        ;;
    *)
        main
        ;;
esac
