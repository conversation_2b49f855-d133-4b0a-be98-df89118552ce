# Azure Container Apps 故障排查指南

本文档记录了在部署 Azure Container Apps 过程中遇到的常见问题和解决方案。

## 🚨 常见问题速查

### 问题 1: 容器启动后外部无法访问

**症状**:
- Container App 状态显示 "Running"
- 健康检查失败或超时
- 外部 curl 请求无响应

**原因**: 应用只监听 localhost，容器外部无法访问

**解决方案**:
```typescript
// ❌ 错误配置
Bun.serve({
    port: 3000,
    // 默认只监听 localhost
});

// ✅ 正确配置
Bun.serve({
    port: 3000,
    hostname: "0.0.0.0", // 监听所有接口
});
```

**验证方法**:
```bash
# 检查应用日志
az containerapp logs show --name <app-name> --resource-group <rg> --tail=20

# 测试健康检查
curl https://<app-url>/health
```

---

### 问题 2: Docker 镜像推送认证失败

**症状**:
```
unauthorized: authentication required
Error saving credentials: docker-credential-osxkeychain: executable file not found
```

**原因**: Docker 客户端与 ACR 认证复杂，凭据助手问题

**解决方案**:
```bash
# ❌ 避免使用 docker push
docker build -t myregistry.azurecr.io/app:latest .
docker push myregistry.azurecr.io/app:latest

# ✅ 使用 ACR 云端构建
az acr build --registry myregistry --image app:latest .
```

**优势**:
- 无需本地 Docker 认证
- 构建在云端进行，速度更快
- 自动推送到 ACR

---

### 问题 3: Dockerfile 构建失败

**症状**:
```
unable to understand line FROM --platform=linux/arm64
failed to scan dependencies: exit status 1
```

**原因**: ACR 构建不支持 `--platform` 参数

**解决方案**:
```dockerfile
# ❌ 错误
FROM --platform=linux/arm64 oven/bun:latest AS base

# ✅ 正确
FROM oven/bun:latest AS base
```

---

### 问题 4: 包管理器命令错误

**症状**:
```
/bin/sh: apk: command not found
addgroup: unrecognized option: -S
```

**原因**: Bun 镜像基于 Debian，不是 Alpine

**解决方案**:
```dockerfile
# ❌ Alpine 语法
RUN apk add --no-cache curl
RUN addgroup -g 1001 -S bunuser && adduser -S bunuser -u 1001

# ✅ Debian 语法
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*
RUN groupadd -g 1001 bunuser && useradd -r -u 1001 -g bunuser bunuser
```

---

### 问题 5: Pulumi Stack Reference 错误

**症状**:
```
error: failed to load stack reference: stack not found
```

**原因**: Stack 引用路径格式错误

**解决方案**:
```yaml
# ❌ 错误格式
config:
  app:infraStackRef: "a1d/a1d-azure/dev"

# ✅ 正确格式 (organization/project/stack)
config:
  app:infraStackRef: "ethan-huo-org/a1d-pulumi-azure/azure-dev"
```

**验证方法**:
```bash
# 查看组织名
pulumi org get-default

# 查看项目和 stack
cd cloud/azure && pulumi stack ls
```

---

### 问题 6: Container App 部署超时

**症状**:
```
ContainerAppOperationError: Failed to provision revision
Operation expired
```

**原因**:
- 镜像拉取失败
- ACR 认证问题
- 应用启动超时

**排查步骤**:

1. **检查镜像是否存在**:
```bash
az acr repository show --name <registry> --repository <image>
```

2. **检查 ACR 认证配置**:
```typescript
// 确保使用管理员凭据
registries: [{
    server: acrLoginServer,
    username: acrCredentials.apply(creds => creds.username || ""),
    passwordSecretRef: "acr-password",
}],
secrets: [{
    name: "acr-password",
    value: acrCredentials.apply(creds => creds.passwords?.[0]?.value || ""),
}],
```

3. **检查应用启动时间**:
```typescript
// 调整健康检查配置
probes: [{
    type: azure.app.Type.Readiness,
    httpGet: {
        path: "/health",
        port: port,
    },
    initialDelaySeconds: 10, // 增加初始延迟
    periodSeconds: 10,
}],
```

---

## 🔧 调试工具和命令

### 查看应用状态
```bash
# 应用概览
az containerapp show --name <app> --resource-group <rg> \
    --query "{name:name,status:properties.provisioningState,fqdn:properties.configuration.ingress.fqdn}"

# Revision 状态
az containerapp revision list --name <app> --resource-group <rg> \
    --query "[].{name:name,status:properties.provisioningState,active:properties.active}"
```

### 查看日志
```bash
# 应用日志
az containerapp logs show --name <app> --resource-group <rg> --tail=50

# 实时日志
az containerapp logs show --name <app> --resource-group <rg> --follow
```

### 测试连接
```bash
# 健康检查
curl -v https://<app-url>/health

# JSON 格式化
curl -s https://<app-url>/health | jq .
```

### ACR 相关
```bash
# 登录 ACR
az acr login --name <registry>

# 列出镜像
az acr repository list --name <registry>

# 查看镜像标签
az acr repository show-tags --name <registry> --repository <image>
```

## 🚀 最佳实践

### 1. 渐进式部署
- 先部署基础设施
- 再构建和推送镜像
- 最后部署应用

### 2. 配置验证
- 使用检查清单验证所有配置
- 在本地先测试 Docker 镜像
- 确认 Stack Reference 路径

### 3. 监控和日志
- 部署后立即检查应用状态
- 保留详细的部署日志
- 设置适当的健康检查参数

### 4. 环境一致性
- 使用相同的 Dockerfile 语法
- 保持开发和生产环境配置一致
- 版本管理 Pulumi 配置文件

## 📚 相关文档

- [DEPLOYMENT.md](./DEPLOYMENT.md) - 详细部署指南
- [Azure Container Apps 官方文档](https://docs.microsoft.com/en-us/azure/container-apps/)
- [Pulumi Azure Native Provider](https://www.pulumi.com/registry/packages/azure-native/)