name: Build and Deploy Azure Backend App

on:
  push:
    branches: [main, develop]
    paths:
      - 'examples/azure-backend-app/**'
  pull_request:
    branches: [main]
    paths:
      - 'examples/azure-backend-app/**'
  workflow_dispatch:

env:
  REGISTRY: a1dazureacr.azurecr.io
  IMAGE_NAME: azure-backend-app
  WORKING_DIRECTORY: examples/azure-backend-app

jobs:
  build:
    runs-on: ubuntu-latest
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        platforms: linux/arm64

    - name: Log in to Azure Container Registry
      uses: azure/docker-login@v1
      with:
        login-server: ${{ env.REGISTRY }}
        username: ${{ secrets.ACR_USERNAME }}
        password: ${{ secrets.ACR_PASSWORD }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: ${{ env.WORKING_DIRECTORY }}
        platforms: linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
      with:
        bun-version: latest

    - name: Install dependencies
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: bun install

    - name: Configure Pulumi
      uses: pulumi/actions@v4
      with:
        command: version
        stack-name: ${{ github.ref == 'refs/heads/main' && 'prod' || 'dev' }}
        work-dir: ${{ env.WORKING_DIRECTORY }}
      env:
        PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}

    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}

    - name: Deploy with Pulumi
      uses: pulumi/actions@v4
      with:
        command: up
        stack-name: ${{ github.ref == 'refs/heads/main' && 'prod' || 'dev' }}
        work-dir: ${{ env.WORKING_DIRECTORY }}
        upsert: true
      env:
        PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
        PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_CONFIG_PASSPHRASE }}
      with-config: |
        azure-backend-app:containerImage=${{ needs.build.outputs.image-tag }}
        azure-backend-app:infraStackRef=a1d/a1d-azure/${{ github.ref == 'refs/heads/main' && 'prod' || 'dev' }}

    - name: Comment PR with deployment info
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const output = `
          ## 🚀 Deployment Preview
          
          **Image**: \`${{ needs.build.outputs.image-tag }}\`
          **Environment**: \`dev\`
          **Stack**: \`a1d/azure-backend-app/dev\`
          
          The application will be deployed to the development environment for testing.
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: output
          });

  health-check:
    needs: [build, deploy]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: Wait for deployment
      run: sleep 60

    - name: Health check
      run: |
        # This would need the actual URL from Pulumi outputs
        # For now, we'll just verify the deployment completed
        echo "Deployment completed successfully"
        echo "Image: ${{ needs.build.outputs.image-tag }}"
        echo "Digest: ${{ needs.build.outputs.image-digest }}"
