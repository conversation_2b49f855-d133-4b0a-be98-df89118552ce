{"name": "azure-backend-app", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "bun run --watch src/index.ts", "start": "bun run src/index.ts", "build:docker": "docker build -t azure-backend-app .", "deploy": "pulumi up", "deploy:preview": "pulumi preview"}, "dependencies": {"@ai-sdk/groq": "2.0.0-beta.4", "@orpc/client": "^1.7.2", "@orpc/server": "^1.7.2", "ai": "5.0.0-beta.21", "arktype": "^2.1.20"}, "devDependencies": {"@pulumi/azure-native": "^2.0.0", "@pulumi/pulumi": "^3.0.0", "@types/bun": "latest", "typescript": "^5.8.3"}}