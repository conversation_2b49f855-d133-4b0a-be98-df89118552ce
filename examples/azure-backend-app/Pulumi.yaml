name: azure-backend-app
runtime: nodejs
description: Azure Backend App deployment using Container Apps

config:
  # Reference to the shared infrastructure stack
  azure-backend-app:infraStackRef:
    description: "Reference to the shared infrastructure stack (format: org/project/stack)"
    default: "ethan-huo-org/a1d-pulumi-azure/azure-dev"

  azure-backend-app:appName:
    description: "Name of the application"
    default: "backend-api-app"

  azure-backend-app:containerImage:
    description: "Container image to deploy"
    default: "a1dazureacr.azurecr.io/azure-backend-app:latest"

  azure-backend-app:replicas:
    description: "Number of container replicas"
    default: 1

  azure-backend-app:cpu:
    description: "CPU allocation (in cores)"
    default: 0.25

  azure-backend-app:memory:
    description: "Memory allocation (in Gi)"
    default: 0.5

  azure-backend-app:port:
    description: "Application port"
    default: 3000

  # Custom Domain Configuration
  azure-backend-app:subdomain:
    description: "Subdomain for the application (e.g., 'api' for api.a1d.ai)"
    default: "pulumi-sample-app"

  azure-backend-app:baseDomain:
    description: "Base domain for the application"
    default: "a1d.ai"

  azure-backend-app:cloudflareZoneId:
    description: "Cloudflare Zone ID for DNS management (a1d.ai zone)"
    default: "ba6da7ae2d88155662bdc76291ab42cc"

template:
  config:
    pulumi:tags:
      value:
        Project: "a1d-azure"
        Environment: "development"
        Component: "backend-app"
        Platform: "azure"
