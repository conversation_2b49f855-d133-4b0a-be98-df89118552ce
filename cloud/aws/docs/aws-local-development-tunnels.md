# 本地开发隧道指南

## 概述

本指南介绍如何在本地开发环境中安全地访问部署在 AWS 私有子网中的 PostgreSQL 数据库和 Redis 缓存。

## 网络架构

```mermaid
graph TB
    subgraph "AWS VPC (10.0.0.0/16)"
        subgraph "Public Subnet"
            direction TB
            Bastion["🖥️ Bastion Host<br/>(t4g.nano ARM)"]
            App["📱 应用容器<br/>(Fargate)"]
        end

        subgraph "Private Subnet"
            direction TB
            DB["🗄️ PostgreSQL<br/>(db.t3.micro)"]
            Redis["⚡ Redis Cache<br/>(cache.t3.micro)"]
        end

        Bastion -.-> DB
        Bastion -.-> Redis
        App --> DB
        App --> Redis
    end

    subgraph "本地开发环境"
        Dev["💻 开发者"]
        LocalDB["localhost:5432"]
        LocalRedis["localhost:6379"]
    end

    Dev -->|"SSH隧道/Session Manager"| Bastion
    Bastion -.->|"端口转发"| LocalDB
    Bastion -.->|"端口转发"| LocalRedis

    Internet["🌐 Internet"] --> App

    style Bastion fill:#e1f5fe
    style App fill:#e8f5e8
    style DB fill:#fff3e0
    style Redis fill:#fce4ec
    style Dev fill:#f3e5f5
```

![网络架构图](../上面的图表展示了完整的网络架构)

## 访问方案对比

| 方案 | 安全性 | 易用性 | 成本 | 推荐场景 |
|------|--------|--------|------|----------|
| **AWS Session Manager** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ~$3.5/月 | **推荐** |
| **SSH 隧道** | ⭐⭐⭐⭐ | ⭐⭐⭐ | ~$3.5/月 | 传统开发者 |
| **本地 Redis 开发** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 免费 | 纯本地开发 |

## 方案一：AWS Session Manager 隧道 (推荐)

### 特点
- ✅ 最安全：无需公网 IP 或 SSH 密钥
- ✅ IAM 权限控制
- ✅ 审计日志记录
- ✅ 一键启动脚本

### 前置条件

1. **安装 Session Manager 插件**：

```bash
# macOS
brew install --cask session-manager-plugin
```

2. **验证安装**：
```bash
session-manager-plugin --version
```

### 手动使用

#### 启动数据库隧道

```bash
# 获取 Bastion 实例 ID (需要先部署基础设施)
BASTION_ID=$(pulumi stack output bastionInstanceId -s ethan-huo-org/a1d-pulumi/dev)

# 启动 PostgreSQL 隧道 (本地 5432 → 远程 5432)
aws ssm start-session \
    --target $BASTION_ID \
    --document-name AWS-StartPortForwardingSession \
    --parameters '{"portNumber":["5432"],"localPortNumber":["5432"]}'
```

#### 启动 Redis 隧道
```bash
# 启动 Redis 隧道 (本地 6379 → 远程 6379)
aws ssm start-session \
    --target $BASTION_ID \
    --document-name AWS-StartPortForwardingSession \
    --parameters '{"portNumber":["6379"],"localPortNumber":["6379"]}'
```

### 自动化脚本

创建 `scripts/dev-tunnel.sh`：

```bash
#!/bin/bash
set -e

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

STACK_NAME="ethan-huo-org/a1d-pulumi/dev"

echo -e "${BLUE}🚇 启动开发隧道...${NC}"

# 检查 Session Manager 插件
if ! command -v session-manager-plugin &> /dev/null; then
    echo -e "${RED}❌ Session Manager 插件未安装${NC}"
    echo -e "${YELLOW}请运行: brew install --cask session-manager-plugin${NC}"
    exit 1
fi

# 获取基础设施信息
echo -e "${YELLOW}📋 获取基础设施信息...${NC}"

BASTION_ID=$(pulumi stack output bastionInstanceId -s $STACK_NAME)
DB_ENDPOINT=$(pulumi stack output postgresEndpoint -s $STACK_NAME)
REDIS_ENDPOINT=$(pulumi stack output redisEndpoint -s $STACK_NAME)

if [ -z "$BASTION_ID" ]; then
    echo -e "${RED}❌ 无法获取 Bastion 实例 ID${NC}"
    echo -e "${YELLOW}请确保已部署基础设施：pulumi up${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Bastion ID: $BASTION_ID${NC}"
echo -e "${GREEN}✓ DB Endpoint: $DB_ENDPOINT${NC}"
echo -e "${GREEN}✓ Redis Endpoint: $REDIS_ENDPOINT${NC}"

# 启动隧道
echo -e "${YELLOW}🔗 启动 PostgreSQL 隧道 (localhost:5432)...${NC}"
aws ssm start-session \
    --target $BASTION_ID \
    --document-name AWS-StartPortForwardingSession \
    --parameters "{\"portNumber\":[\"5432\"],\"localPortNumber\":[\"5432\"]}" &

echo -e "${YELLOW}🔗 启动 Redis 隧道 (localhost:6379)...${NC}"
aws ssm start-session \
    --target $BASTION_ID \
    --document-name AWS-StartPortForwardingSession \
    --parameters "{\"portNumber\":[\"6379\"],\"localPortNumber\":[\"6379\"]}" &

echo -e "${GREEN}✅ 隧道已启动！${NC}"
echo -e "${BLUE}📝 连接信息：${NC}"
echo -e "   PostgreSQL: postgresql://postgres:YOUR_PASSWORD@localhost:5432/maindb"
echo -e "   Redis: redis://localhost:6379"
echo -e "${YELLOW}💡 按 Ctrl+C 停止隧道${NC}"

# 等待用户中断
wait
```

## 方案二：SSH 隧道

### 前置条件

1. **创建 EC2 密钥对**：
   ```bash
   # 在 AWS Console 中创建密钥对，或使用 CLI
   aws ec2 create-key-pair --key-name bastion-key --query 'KeyMaterial' --output text > ~/.ssh/bastion-key.pem
   chmod 400 ~/.ssh/bastion-key.pem
   ```

2. **获取 Bastion 公网 IP**：
   ```bash
   BASTION_IP=$(pulumi stack output bastionPublicIp -s ethan-huo-org/a1d-pulumi/dev)
   ```

### 使用方法

```bash
# PostgreSQL 隧道
ssh -i ~/.ssh/bastion-key.pem -L 5432:$DB_ENDPOINT:5432 ec2-user@$BASTION_IP -N

# Redis 隧道
ssh -i ~/.ssh/bastion-key.pem -L 6379:$REDIS_ENDPOINT:6379 ec2-user@$BASTION_IP -N
```

## 方案三：本地 Redis 开发

### 适用场景
- 纯本地开发
- 快速原型开发
- 无需缓存数据持久化

### 使用方法

```bash
# 本地安装 Redis
brew install redis

# 启动本地 Redis
brew services start redis

# 或临时启动
redis-server
```

### 环境变量配置

```bash
# .env.local (本地开发)
DATABASE_URL=postgresql://postgres:YOUR_PASSWORD@localhost:5432/maindb  # 通过隧道
REDIS_URL=redis://localhost:6379  # 本地 Redis

# 生产环境自动使用 AWS Redis
```

## 本地应用配置

### 环境变量设置

创建 `.env.local`：

```bash
# 数据库连接（通过隧道）
DATABASE_URL=postgresql://postgres:YOUR_PASSWORD@localhost:5432/maindb

# Redis 连接选择
# 选项1: 通过隧道连接 AWS Redis
REDIS_URL=redis://localhost:6379

# 选项2: 使用本地 Redis（推荐开发）
# REDIS_URL=redis://127.0.0.1:6379

# AWS 配置
AWS_REGION=ap-southeast-2
DB_SECRET_ARN=arn:aws:secretsmanager:ap-southeast-2:ACCOUNT:secret:db-secret-XXXXX
```

### Node.js 示例代码

```javascript
// db.js - 数据库连接
const { Pool } = require('pg');
const redis = require('redis');

// PostgreSQL 连接
const pgPool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

// Redis 连接
const redisClient = redis.createClient({
    url: process.env.REDIS_URL,
});

// 测试连接
async function testConnections() {
    try {
        // 测试 PostgreSQL
        const pgResult = await pgPool.query('SELECT NOW()');
        console.log('✅ PostgreSQL 连接成功:', pgResult.rows[0]);

        // 测试 Redis
        await redisClient.connect();
        await redisClient.set('test', 'hello');
        const redisResult = await redisClient.get('test');
        console.log('✅ Redis 连接成功:', redisResult);

    } catch (error) {
        console.error('❌ 连接失败:', error);
    }
}

module.exports = { pgPool, redisClient, testConnections };
```

## 常用命令集合

### 快速启动脚本 (`scripts/dev-start.sh`)

```bash
#!/bin/bash

echo "🚀 启动本地开发环境..."

# 1. 启动隧道
echo "启动数据库隧道..."
./scripts/dev-tunnel.sh &
TUNNEL_PID=$!

# 2. 等待隧道建立
sleep 5

# 3. 运行数据库迁移
echo "运行数据库迁移..."
npm run db:migrate

# 4. 启动开发服务器
echo "启动开发服务器..."
npm run dev

# 清理
trap "kill $TUNNEL_PID" EXIT
```

### 数据库工具脚本

```bash
#!/bin/bash
# scripts/db-tools.sh

case "$1" in
    "connect")
        psql postgresql://postgres:$DB_PASSWORD@localhost:5432/maindb
        ;;
    "dump")
        pg_dump postgresql://postgres:$DB_PASSWORD@localhost:5432/maindb > backup.sql
        ;;
    "restore")
        psql postgresql://postgres:$DB_PASSWORD@localhost:5432/maindb < backup.sql
        ;;
    "redis-cli")
        redis-cli -h localhost -p 6379
        ;;
    *)
        echo "用法: $0 {connect|dump|restore|redis-cli}"
        ;;
esac
```

## 故障排除

### 常见问题

#### 1. Session Manager 连接失败

```bash
# 检查实例状态
aws ssm describe-instance-information --filters "Key=InstanceIds,Values=$BASTION_ID"

# 检查 SSM Agent 状态
aws ssm send-command --instance-ids $BASTION_ID --document-name "AWS-RunShellScript" --parameters 'commands=["systemctl status amazon-ssm-agent"]'
```

#### 2. 端口已被占用

```bash
# 查看端口占用
lsof -i :5432
lsof -i :6379

# 杀死占用进程
kill -9 $(lsof -t -i:5432)
```

#### 3. 数据库连接超时

```bash
# 检查安全组规则
aws ec2 describe-security-groups --group-ids $DB_SECURITY_GROUP_ID

# 测试网络连通性
telnet localhost 5432
```

#### 4. Redis 连接失败

```bash
# Redis 连接测试
redis-cli -h localhost -p 6379 ping

# 检查 Redis 配置
aws elasticache describe-replication-groups --replication-group-id redis-cache
```

### 调试工具

```bash
# 网络连接测试
nc -zv localhost 5432
nc -zv localhost 6379

# 进程监控
ps aux | grep session-manager
ps aux | grep ssh

# 日志查看
tail -f ~/.session-manager-logs/
```

## 安全最佳实践

1. **定期轮换密钥**：
   ```bash
   # 更新数据库密码
   pulumi config set --secret dbPassword "NEW_PASSWORD"
   pulumi up
   ```

2. **限制访问时间**：
   - 使用临时凭证
   - 设置自动断开隧道
   - 按需启停 Bastion

3. **监控访问日志**：
   ```bash
   # 查看 CloudTrail 日志
   aws logs filter-log-events --log-group-name CloudTrail/SessionManager
   ```

4. **网络分割**：
   - 数据库只在私有子网
   - 最小权限原则
   - VPC 内网络隔离

## 成本分析

| 访问方案 | 月费用 | 说明 |
|----------|--------|------|
| **Bastion + 隧道** | ~$3.10 | ARM Graviton2 更便宜 |
| **本地 Redis** | 免费 | 降低复杂性 |
| **Session Manager** | 免费 | AWS 服务无额外费用 |
| **数据传输** | ~$0.10 | VPC 内传输免费 |
| **总计** | **~$3.20** | 可按需启停 |

### 成本优化

```bash
# 仅在开发时启动 Bastion (t4g.nano ARM)
aws ec2 start-instances --instance-ids $BASTION_ID

# 开发结束后停止（停止时不计费）
aws ec2 stop-instances --instance-ids $BASTION_ID
```

## 自动化 VS Code 集成

### VS Code 配置 (`/.vscode/tasks.json`)

```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Start Dev Tunnels",
            "type": "shell",
            "command": "./scripts/dev-tunnel.sh",
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new"
            },
            "problemMatcher": []
        },
        {
            "label": "Connect to Database",
            "type": "shell",
            "command": "./scripts/db-tools.sh connect",
            "group": "build",
            "dependsOn": "Start Dev Tunnels"
        }
    ]
}
```

现在您可以通过 `Cmd+Shift+P` → `Tasks: Run Task` → `Start Dev Tunnels` 来启动隧道了！