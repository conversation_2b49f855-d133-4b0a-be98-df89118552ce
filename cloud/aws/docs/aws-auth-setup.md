# AWS 本地开发认证配置指南

## 🎯 目标

让的本地开发环境能够访问 AWS 服务（如 Secrets Manager）

## 📋 前提条件

- ✅ 你有 AWS Console 访问权限（能登录 AWS 网页）
- ✅ 你的账号有管理员权限或相关服务权限

## 🔧 配置步骤

### 第 1 步：创建 Access Key

**1.1 登录 AWS Console**

- 打开 https://console.aws.amazon.com
- 用你的账号密码登录

**1.2 进入 IAM 服务**

- 在搜索框输入 "IAM"
- 点击 "IAM" 服务

**1.3 创建 Access Key**

```
导航路径：IAM → Users → [你的用户名] → Security credentials → Access keys
```

- 点击 **"Create access key"**
- 选择 **"Command Line Interface (CLI)"**
- 勾选确认框：**"I understand the above recommendation..."**
- 点击 **"Next"**
- (可选) 添加描述：`Local development`
- 点击 **"Create access key"**

**1.4 ⚠️ 重要：保存密钥**

```
Access key ID: AKIAIOSFODNN7EXAMPLE
Secret access key: wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
```

**🚨 立即复制保存！** 关闭页面后无法再查看 Secret access key

### 第 2 步：安装 AWS CLI

```bash
# macOS
brew install awscli

# 验证安装
aws --version
```

### 第 3 步：配置 AWS CLI

```bash
aws configure
```

按提示输入（用第 1 步保存的密钥）：

```
AWS Access Key ID [None]: AKIAIOSFODNN7EXAMPLE
AWS Secret Access Key [None]: wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
Default region name [None]: ap-southeast-2
Default output format [None]: json
```

### 第 4 步：验证配置

```bash
# 测试认证
aws sts get-caller-identity

# 期望输出：
{
    "UserId": "AIDAIOSFODNN7EXAMPLE",
    "Account": "************",
    "Arn": "arn:aws:iam::************:user/your-username"
}
```

✅ **如果看到这个输出，说明配置成功！**

## 🛠️ 使用示例

### 现在你的应用可以访问 AWS 服务了

```typescript
import { type } from "arktype";

// 在你的 TypeScript 项目中
import {
	SecretsManagerClient,
	GetSecretValueCommand,
} from "@aws-sdk/client-secrets-manager";

const EnvSchema = type({
	DB_SECRET_ARN: string,
});

const env = EnvSchema.assert(process.env);

async function getDBCredentials() {
	// AWS SDK 自动使用 aws configure 设置的凭证
	const client = new SecretsManagerClient({ region: "ap-southeast-2" });

	const command = new GetSecretValueCommand({
		SecretId: env.DB_SECRET_ARN, // 从 .env.local 读取
	});

	try {
		const secret = await client.send(command);
		const credentials = JSON.parse(secret.SecretString!);

		console.log("✅ 成功获取数据库凭证");
		return credentials;
	} catch (error) {
		console.error("❌ 获取凭证失败:", error.message);
		throw error;
	}
}
```

### 或者测试 Secrets Manager 访问

```bash
# 命令行测试
aws secretsmanager get-secret-value --secret-id your-secret-arn
```

## 🔒 安全最佳实践

### ✅ 推荐做法

1. **定期轮换 Access Key**（3-6 个月）
2. **只给必要的权限**
3. **不要把 Access Key 提交到 Git**

### ❌ 避免的做法

1. **不要分享 Access Key**
2. **不要把密钥放在代码里**
3. **不要用根账号创建 Access Key**

## 🔍 权限设置

如果你的账号没有足够权限，可能需要管理员添加以下策略：

```json
{
	"Version": "2012-10-17",
	"Statement": [
		{
			"Effect": "Allow",
			"Action": [
				"secretsmanager:GetSecretValue",
				"secretsmanager:DescribeSecret"
			],
			"Resource": "*"
		}
	]
}
```

## 🐛 常见问题排查

### 问题 1：`aws sts get-caller-identity` 失败

```bash
# 检查配置文件
cat ~/.aws/credentials
cat ~/.aws/config

# 重新配置
aws configure
```

### 问题 2：权限被拒绝

```
An error occurred (AccessDenied) when calling the GetSecretValue operation
```

**解决**：联系管理员添加 Secrets Manager 权限

### 问题 3：找不到 Secret

```
An error occurred (ResourceNotFoundException) when calling the GetSecretValue operation
```

**解决**：检查 `DB_SECRET_ARN` 是否正确

## 📁 配置文件位置

AWS CLI 配置保存在：

```bash
~/.aws/credentials  # Access Key 信息
~/.aws/config       # 区域等配置
```

## 🎉 完成

现在你已经配置好了 AWS 认证，可以：

1. ✅ 使用 `pulumi up` 部署基础设施
2. ✅ 通过 `bun run generate-env` 生成 .env.local
3. ✅ 使用 Session Manager 连接到 EC2 实例

**下一步**：部署你的基础设施 `pulumi up`，然后运行 `bun run generate-env` 来生成项目的环境变量文件！
