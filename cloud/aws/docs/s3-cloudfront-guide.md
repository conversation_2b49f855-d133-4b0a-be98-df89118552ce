# S3 + CloudFront CDN 使用指南

## 架构概述

我们的基础设施包含完整的 S3 + CloudFront CDN 配置，用于高效分发静态资源。

### 🏗️ 架构组件

```
用户请求 → CloudFront (CDN) → S3 Bucket (源站)
                ↓
         全球边缘节点缓存
```

## 🚀 特性

- **🔒 安全访问**: 使用 Origin Access Identity (OAI) 确保只有 CloudFront 能访问 S3
- **⚡ 智能缓存**: 不同文件类型有针对性的缓存策略
- **🌍 全球分发**: 利用 CloudFront 的全球边缘节点
- **💰 成本优化**: 使用 PriceClass_100 (北美+欧洲边缘节点)
- **🔐 HTTPS**: 强制 HTTPS 访问

## 📁 缓存策略

| 文件类型 | 缓存时间 | 说明 |
|---------|----------|------|
| HTML    | 1小时    | 主要内容，中等缓存 |
| CSS/JS  | 1天      | 样式和脚本，长缓存 |
| 图片    | 1天      | 静态图片，长缓存 |
| API     | 不缓存   | 动态内容，直接穿透 |

## 🛠️ 使用方法

### 1. 部署基础设施

```bash
# 部署 Pulumi 栈
pulumi up

# 导出配置
bun run scripts/generate-env.ts
```

### 2. 创建和上传示例文件

```bash
# 创建示例文件
bun run scripts/s3-cdn-example.ts create-samples

# 上传示例文件
bun run scripts/s3-cdn-example.ts upload-samples

# 查看 CDN URLs
bun run scripts/s3-cdn-example.ts show-urls
```

### 3. 单独上传文件

```bash
# 上传单个文件
bun run scripts/s3-cdn-example.ts upload path/to/file.html

# 指定 S3 键名
bun run scripts/s3-cdn-example.ts upload path/to/file.html custom-name.html
```

### 4. 缓存管理

```bash
# 使特定路径的缓存失效
bun run scripts/s3-cdn-example.ts invalidate "/index.html"

# 使所有缓存失效
bun run scripts/s3-cdn-example.ts invalidate "/*"

# 使多个路径的缓存失效
bun run scripts/s3-cdn-example.ts invalidate "/css/*" "/js/*"
```

## 🔧 配置说明

### CloudFront 配置

```typescript
// 默认缓存行为
defaultCacheBehavior: {
  viewerProtocolPolicy: "redirect-to-https",  // 强制 HTTPS
  compress: true,                             // 启用 Gzip 压缩
  defaultTtl: 3600,                          // 默认 1 小时缓存
}

// 针对不同文件类型的缓存行为
orderedCacheBehaviors: [
  {
    pathPattern: "/api/*",     // API 路径不缓存
    defaultTtl: 0,
  },
  {
    pathPattern: "*.css",      // CSS 文件长缓存
    defaultTtl: 86400,         // 1 天
  },
  // ... 更多配置
]
```

### S3 安全配置

```typescript
// Origin Access Identity 确保安全访问
originAccessIdentity: {
  comment: "OAI for S3 bucket access"
}

// Bucket Policy 只允许 CloudFront 访问
bucketPolicy: {
  Statement: [{
    Effect: "Allow",
    Principal: { AWS: oaiArn },
    Action: "s3:GetObject",
    Resource: "bucket-arn/*"
  }]
}
```

## 📊 监控和优化

### CloudWatch 指标

- **缓存命中率**: 监控 CDN 效率
- **源站请求数**: 监控 S3 负载
- **错误率**: 监控服务可用性

### 成本优化

1. **缓存策略**: 静态资源长缓存，减少源站请求
2. **压缩**: 启用 Gzip 压缩减少传输量
3. **地理限制**: 使用 PriceClass_100 限制边缘节点范围

## 🔄 常见工作流

### 网站部署工作流

```bash
# 1. 构建静态网站
npm run build

# 2. 上传到 S3
aws s3 sync ./dist s3://your-bucket-name --delete

# 3. 使 CloudFront 缓存失效
bun run scripts/s3-cdn-example.ts invalidate "/*"
```

### 快速更新单个文件

```bash
# 1. 上传文件
bun run scripts/s3-cdn-example.ts upload new-file.html

# 2. 使特定文件的缓存失效
bun run scripts/s3-cdn-example.ts invalidate "/new-file.html"
```

## 🌐 访问 URLs

部署完成后，你将获得以下 URLs：

- **S3 直接访问**: `https://bucket-name.s3.region.amazonaws.com/file`
- **CDN 访问** (推荐): `https://distribution-id.cloudfront.net/file`

⚠️ **建议**: 始终使用 CDN URL 访问，享受缓存加速和全球分发。

## 🔍 故障排除

### 常见问题

1. **404 错误**: 检查文件是否正确上传到 S3
2. **缓存未更新**: 执行缓存失效操作
3. **访问被拒绝**: 检查 Bucket Policy 和 OAI 配置

### 调试步骤

```bash
# 检查 S3 文件
aws s3 ls s3://your-bucket-name --recursive

# 检查 CloudFront 状态
aws cloudfront get-distribution --id DISTRIBUTION_ID

# 查看缓存失效状态
aws cloudfront list-invalidations --distribution-id DISTRIBUTION_ID
```

## 📈 最佳实践

1. **版本控制**: 为静态资源添加版本号或哈希
2. **缓存策略**: 根据文件更新频率设置合适的缓存时间
3. **压缩**: 启用 Gzip 压缩减少传输时间
4. **监控**: 定期检查缓存命中率和错误率
5. **安全**: 使用 HTTPS 和适当的 CORS 策略

---

通过这个配置，你可以获得高性能、安全、成本效益的静态资源分发服务。🚀