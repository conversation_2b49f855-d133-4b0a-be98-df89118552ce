import { type } from 'arktype'
import { lazy } from './lazy'

export const runtimeEnvSchema = type({
  // ----------------------------------
  // databases
  // ----------------------------------
  POSTGRES_URL: 'string.url',
  REDIS_URL: 'string.url',

  // ----------------------------------
  // integrations
  // ----------------------------------
})

// export const env = runtimeEnvSchema.assert(process.env)

export const env = lazy(() => runtimeEnvSchema.assert(process.env))

export type RuntimeEnv = typeof runtimeEnvSchema.infer

declare global {
  namespace NodeJS {
    interface ProcessEnv extends RuntimeEnv {}
  }
}
