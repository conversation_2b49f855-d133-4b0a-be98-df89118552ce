import * as pulumi from "@pulumi/pulumi";
import * as aws from "@pulumi/aws";

// Get configuration from Pulumi config
const config = new pulumi.Config();
const projectName = config.get("projectName") || "a1d";

// Create a simple S3 bucket for testing
const bucket = new aws.s3.BucketV2("test-bucket", {
    tags: {
        Project: projectName,
        Environment: "development",
    },
});

// Export the bucket name
export const bucketName = bucket.id;
export const bucketArn = bucket.arn;

// Simple test message
export const message = "AWS connection successful! S3 bucket created.";