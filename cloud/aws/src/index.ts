import * as pulumi from "@pulumi/pulumi";
import * as aws from "@pulumi/aws";
import * as awsx from "@pulumi/awsx";

// Get configuration from Pulumi config
const config = new pulumi.Config();
const dbPassword = config.requireSecret("dbPassword");
const projectName = config.get("projectName") || "a1d-dev";

// Define common tags for all resources
const commonTags = {
	Project: projectName,
	Environment: "development",
	CostCenter: "engineering",
};

// Create an AWS resource (S3 Bucket)
const bucket = new aws.s3.BucketV2(`${projectName}-bucket`, {
	tags: commonTags,
});

// Configure S3 bucket for static website hosting
const bucketPublicAccessBlock = new aws.s3.BucketPublicAccessBlock(
	`${projectName}-bucket-pab`,
	{
		bucket: bucket.id,
		blockPublicAcls: false,
		blockPublicPolicy: false,
		ignorePublicAcls: false,
		restrictPublicBuckets: false,
	}
);

// Create CloudFront Origin Access Identity for secure S3 access
const originAccessIdentity = new aws.cloudfront.OriginAccessIdentity(
	`${projectName}-oai`,
	{
		comment: `OAI for ${projectName} S3 bucket`,
	}
);

// Create S3 bucket policy to allow CloudFront access
const bucketPolicy = new aws.s3.BucketPolicy(
	`${projectName}-bucket-policy`,
	{
		bucket: bucket.id,
		policy: pulumi.all([bucket.arn, originAccessIdentity.iamArn]).apply(
			([bucketArn, oaiArn]) =>
				JSON.stringify({
					Version: "2012-10-17",
					Statement: [
						{
							Sid: "AllowCloudFrontAccess",
							Effect: "Allow",
							Principal: {
								AWS: oaiArn,
							},
							Action: "s3:GetObject",
							Resource: `${bucketArn}/*`,
						},
					],
				})
		),
	},
	{ dependsOn: [bucketPublicAccessBlock] }
);

// Create CloudFront distribution
const distribution = new aws.cloudfront.Distribution(
	`${projectName}-cdn`,
	{
		origins: [
			{
				domainName: bucket.bucketDomainName,
				originId: "S3-origin",
				s3OriginConfig: {
					originAccessIdentity: originAccessIdentity.cloudfrontAccessIdentityPath,
				},
			},
		],
		enabled: true,
		isIpv6Enabled: true,
		comment: `CDN for ${projectName} static assets`,
		defaultRootObject: "index.html",

		defaultCacheBehavior: {
			allowedMethods: ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"],
			cachedMethods: ["GET", "HEAD"],
			targetOriginId: "S3-origin",

			forwardedValues: {
				queryString: false,
				cookies: {
					forward: "none",
				},
			},

			viewerProtocolPolicy: "redirect-to-https",
			minTtl: 0,
			defaultTtl: 3600,
			maxTtl: 86400,

			compress: true,
		},

		// Cache behaviors for different file types
		orderedCacheBehaviors: [
			{
				pathPattern: "/api/*",
				allowedMethods: ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"],
				cachedMethods: ["GET", "HEAD"],
				targetOriginId: "S3-origin",

				forwardedValues: {
					queryString: true,
					headers: ["Authorization", "Content-Type"],
					cookies: {
						forward: "all",
					},
				},

				viewerProtocolPolicy: "redirect-to-https",
				minTtl: 0,
				defaultTtl: 0,
				maxTtl: 0,
			},
			{
				pathPattern: "*.jpg",
				allowedMethods: ["GET", "HEAD"],
				cachedMethods: ["GET", "HEAD"],
				targetOriginId: "S3-origin",

				forwardedValues: {
					queryString: false,
					cookies: {
						forward: "none",
					},
				},

				viewerProtocolPolicy: "redirect-to-https",
				minTtl: 0,
				defaultTtl: 86400,
				maxTtl: 31536000,
				compress: true,
			},
			{
				pathPattern: "*.png",
				allowedMethods: ["GET", "HEAD"],
				cachedMethods: ["GET", "HEAD"],
				targetOriginId: "S3-origin",

				forwardedValues: {
					queryString: false,
					cookies: {
						forward: "none",
					},
				},

				viewerProtocolPolicy: "redirect-to-https",
				minTtl: 0,
				defaultTtl: 86400,
				maxTtl: 31536000,
				compress: true,
			},
			{
				pathPattern: "*.css",
				allowedMethods: ["GET", "HEAD"],
				cachedMethods: ["GET", "HEAD"],
				targetOriginId: "S3-origin",

				forwardedValues: {
					queryString: false,
					cookies: {
						forward: "none",
					},
				},

				viewerProtocolPolicy: "redirect-to-https",
				minTtl: 0,
				defaultTtl: 86400,
				maxTtl: 31536000,
				compress: true,
			},
			{
				pathPattern: "*.js",
				allowedMethods: ["GET", "HEAD"],
				cachedMethods: ["GET", "HEAD"],
				targetOriginId: "S3-origin",

				forwardedValues: {
					queryString: false,
					cookies: {
						forward: "none",
					},
				},

				viewerProtocolPolicy: "redirect-to-https",
				minTtl: 0,
				defaultTtl: 86400,
				maxTtl: 31536000,
				compress: true,
			},
		],

		priceClass: "PriceClass_100", // Use only North America and Europe edge locations

		restrictions: {
			geoRestriction: {
				restrictionType: "none",
			},
		},

		viewerCertificate: {
			cloudfrontDefaultCertificate: true,
		},

		tags: {
			...commonTags,
			Purpose: "CDN for static assets",
		},
	},
	{ dependsOn: [bucketPolicy] }
);

// Create a VPC for database and cache
const vpc = new awsx.ec2.Vpc(`${projectName}-vpc`, {
	cidrBlock: "10.0.0.0/16",
	enableDnsHostnames: true,
	enableDnsSupport: true,
});

// Create subnet group for RDS (使用私有子网)
const dbSubnetGroup = new aws.rds.SubnetGroup(
	`${projectName}-db-subnet-group`,
	{
		subnetIds: vpc.privateSubnetIds,
		tags: {
			...commonTags,
			Name: "Main DB subnet group",
		},
	}
);

// Create security group for PostgreSQL (只允许VPC内访问)
const dbSecurityGroup = new aws.ec2.SecurityGroup(
	`${projectName}-db-security-group`,
	{
		vpcId: vpc.vpcId,
		description: "Security group for PostgreSQL database",
		ingress: [
			{
				fromPort: 5432,
				toPort: 5432,
				protocol: "tcp",
				cidrBlocks: ["10.0.0.0/16"],
				description: "VPC PostgreSQL access",
			},
		],
		egress: [
			{
				fromPort: 0,
				toPort: 0,
				protocol: "-1",
				cidrBlocks: ["0.0.0.0/0"],
			},
		],
		tags: commonTags,
	}
);

// Create PostgreSQL RDS instance (私有访问)
const postgres = new aws.rds.Instance(`${projectName}-postgres-db`, {
	allocatedStorage: 20,
	engine: "postgres",
	engineVersion: "17.5",
	instanceClass: "db.t4g.micro",  // ARM-based Graviton2 instance
	dbName: "maindb",
	username: "postgres",
	password: dbPassword,
	dbSubnetGroupName: dbSubnetGroup.name,
	vpcSecurityGroupIds: [dbSecurityGroup.id],
	publiclyAccessible: false,
	skipFinalSnapshot: true,
	tags: {
		...commonTags,
		Name: "Main PostgreSQL Database",
	},
});

// Create subnet group for ElastiCache
const cacheSubnetGroup = new aws.elasticache.SubnetGroup(
	`${projectName}-cache-subnet-group`,
	{
		subnetIds: vpc.privateSubnetIds,
		tags: commonTags,
	}
);

// Create security group for Redis (只允许VPC内访问)
const cacheSecurityGroup = new aws.ec2.SecurityGroup(
	`${projectName}-cache-security-group`,
	{
		vpcId: vpc.vpcId,
		description: "Security group for Redis cache",
		ingress: [
			{
				fromPort: 6379,
				toPort: 6379,
				protocol: "tcp",
				cidrBlocks: ["10.0.0.0/16"],
				description: "VPC Redis access",
			},
		],
		egress: [
			{
				fromPort: 0,
				toPort: 0,
				protocol: "-1",
				cidrBlocks: ["0.0.0.0/0"],
			},
		],
		tags: commonTags,
	}
);

// Create Redis ElastiCache cluster
const redis = new aws.elasticache.ReplicationGroup(
	`${projectName}-redis-cache`,
	{
		description: "Redis cache cluster",
		replicationGroupId: "redis-cache",
		nodeType: "cache.t4g.micro",  // ARM-based Graviton2 instance
		port: 6379,
		parameterGroupName: "default.redis7",
		numCacheClusters: 1,
		subnetGroupName: cacheSubnetGroup.name,
		securityGroupIds: [cacheSecurityGroup.id],
		tags: {
			...commonTags,
			Name: "Main Redis Cache",
		},
	}
);

// Create security group for Fargate applications (公网访问)
const fargateSecurityGroup = new aws.ec2.SecurityGroup(
	`${projectName}-fargate-app-sg`,
	{
		vpcId: vpc.vpcId,
		description: "Security group for Fargate applications",
		ingress: [
			{
				fromPort: 80,
				toPort: 80,
				protocol: "tcp",
				cidrBlocks: ["0.0.0.0/0"],
				description: "HTTP access",
			},
			{
				fromPort: 443,
				toPort: 443,
				protocol: "tcp",
				cidrBlocks: ["0.0.0.0/0"],
				description: "HTTPS access",
			},
		],
		egress: [
			{
				fromPort: 0,
				toPort: 0,
				protocol: "-1",
				cidrBlocks: ["0.0.0.0/0"],
			},
		],
		tags: {
			...commonTags,
			Purpose: "Fargate Applications",
		},
	}
);

// Update database security group to allow access from Fargate
const dbFargateIngress = new aws.ec2.SecurityGroupRule(
	`${projectName}-db-fargate-ingress`,
	{
		type: "ingress",
		fromPort: 5432,
		toPort: 5432,
		protocol: "tcp",
		sourceSecurityGroupId: fargateSecurityGroup.id,
		securityGroupId: dbSecurityGroup.id,
		description: "PostgreSQL access from Fargate apps",
	}
);

// Update cache security group to allow access from Fargate
const cacheFargateIngress = new aws.ec2.SecurityGroupRule(
	`${projectName}-cache-fargate-ingress`,
	{
		type: "ingress",
		fromPort: 6379,
		toPort: 6379,
		protocol: "tcp",
		sourceSecurityGroupId: fargateSecurityGroup.id,
		securityGroupId: cacheSecurityGroup.id,
		description: "Redis access from Fargate apps",
	}
);

// Create ECR repository for application images
const ecrRepository = new aws.ecr.Repository(`${projectName}-app-repo`, {
	name: `${projectName}-app`,
	imageTagMutability: "MUTABLE",
	imageScanningConfiguration: {
		scanOnPush: true,
	},
	tags: {
		...commonTags,
		Purpose: "Application Container Images",
	},
});

// Create ECR lifecycle policy
const ecrLifecyclePolicy = new aws.ecr.LifecyclePolicy(
	`${projectName}-app-repo-lifecycle`,
	{
		repository: ecrRepository.name,
		policy: JSON.stringify({
			rules: [
				{
					rulePriority: 1,
					description: "Keep last 10 untagged images",
					selection: {
						tagStatus: "untagged",
						countType: "imageCountMoreThan",
						countNumber: 10,
					},
					action: {
						type: "expire",
					},
				},
				{
					rulePriority: 2,
					description: "Keep last 5 tagged images",
					selection: {
						tagStatus: "tagged",
						countType: "imageCountMoreThan",
						countNumber: 5,
					},
					action: {
						type: "expire",
					},
				},
			],
		}),
	}
);

// Create IAM role for Session Manager
const ssmRole = new aws.iam.Role(`${projectName}-ssm-role`, {
	description: "IAM role for EC2 instances to use Session Manager",
	assumeRolePolicy: JSON.stringify({
		Version: "2012-10-17",
		Statement: [
			{
				Action: "sts:AssumeRole",
				Effect: "Allow",
				Principal: {
					Service: "ec2.amazonaws.com",
				},
			},
		],
	}),
	tags: {
		...commonTags,
		Purpose: "Session Manager Access",
	},
});

// Attach Session Manager policy to role
new aws.iam.RolePolicyAttachment(`${projectName}-ssm-policy-attachment`, {
	role: ssmRole.name,
	policyArn: "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
});

// Create instance profile for the IAM role
const ssmInstanceProfile = new aws.iam.InstanceProfile(
	`${projectName}-ssm-instance-profile`,
	{
		role: ssmRole.name,
		tags: commonTags,
	}
);

// Create security group for Bastion host
const bastionSecurityGroup = new aws.ec2.SecurityGroup(
	`${projectName}-bastion-sg`,
	{
		vpcId: vpc.vpcId,
		description: "Security group for Bastion host - Session Manager only",
		egress: [
			{
				fromPort: 0,
				toPort: 0,
				protocol: "-1",
				cidrBlocks: ["0.0.0.0/0"],
				description: "All outbound traffic",
			},
		],
		tags: {
			...commonTags,
			Purpose: "Bastion Host",
		},
	}
);

// Get the latest Amazon Linux 2 AMI for ARM64
const amazonLinuxAmi = aws.ec2.getAmi({
	mostRecent: true,
	owners: ["amazon"],
	filters: [
		{
			name: "name",
			values: ["amzn2-ami-hvm-*-arm64-gp2"],
		},
		{
			name: "virtualization-type",
			values: ["hvm"],
		},
		{
			name: "architecture",
			values: ["arm64"],
		},
	],
});

// Create Bastion host in public subnet
const bastionHost = new aws.ec2.Instance(`${projectName}-bastion-host`, {
	instanceType: "t4g.nano",
	ami: amazonLinuxAmi.then((ami) => ami.id),
	subnetId: vpc.publicSubnetIds.apply((ids) => ids[0]),
	vpcSecurityGroupIds: [bastionSecurityGroup.id],
	iamInstanceProfile: ssmInstanceProfile.name,
	userData: pulumi.interpolate`#!/bin/bash
# Update system
yum update -y

# Install PostgreSQL 17 client for ARM64
# Add PostgreSQL 17 repository for ARM64
rpm -Uvh https://download.postgresql.org/pub/repos/yum/reporpms/EL-7-aarch64/pgdg-redhat-repo-latest.noarch.rpm
yum install -y postgresql17

# Install Redis client
yum install redis -y

# Ensure SSM agent is running (should be by default on Amazon Linux 2)
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent

# Create a simple test script
cat > /home/<USER>/test-connections.sh << 'EOF'
#!/bin/bash
echo "Testing PostgreSQL connection..."
pg_isready -h ${postgres.endpoint} -p 5432

echo "Testing Redis connection..."
redis-cli -h ${redis.configurationEndpointAddress} -p 6379 ping
EOF

chmod +x /home/<USER>/test-connections.sh
chown ec2-user:ec2-user /home/<USER>/test-connections.sh

# Log setup completion
echo "Bastion host setup completed at $(date)" >> /var/log/setup.log
`,
	tags: {
		...commonTags,
		Name: `${projectName}-bastion`,
		Purpose: "Development Database Access",
	},
});

// Allow Bastion to connect to database
const bastionDbAccess = new aws.ec2.SecurityGroupRule(
	`${projectName}-bastion-db-access`,
	{
		type: "ingress",
		fromPort: 5432,
		toPort: 5432,
		protocol: "tcp",
		sourceSecurityGroupId: bastionSecurityGroup.id,
		securityGroupId: dbSecurityGroup.id,
		description: "PostgreSQL access from Bastion",
	}
);

// Allow Bastion to connect to Redis
const bastionRedisAccess = new aws.ec2.SecurityGroupRule(
	`${projectName}-bastion-redis-access`,
	{
		type: "ingress",
		fromPort: 6379,
		toPort: 6379,
		protocol: "tcp",
		sourceSecurityGroupId: bastionSecurityGroup.id,
		securityGroupId: cacheSecurityGroup.id,
		description: "Redis access from Bastion",
	}
);

// Export S3 and CloudFront information
export const bucketName = bucket.id;
export const bucketDomainName = bucket.bucketDomainName;
export const bucketArn = bucket.arn;

// Export CloudFront CDN information
export const cloudFrontDistributionId = distribution.id;
export const cloudFrontDomainName = distribution.domainName;
export const cloudFrontUrl = pulumi.interpolate`https://${distribution.domainName}`;
export const originAccessIdentityId = originAccessIdentity.id;

// Export VPC and network information for other projects
export const vpcId = vpc.vpcId;
export const publicSubnetIds = vpc.publicSubnetIds;
export const privateSubnetIds = vpc.privateSubnetIds;
export const vpcCidrBlock = "10.0.0.0/16";

// Export security groups for Fargate to use
export const dbSecurityGroupId = dbSecurityGroup.id;
export const cacheSecurityGroupId = cacheSecurityGroup.id;
export const fargateSecurityGroupId = fargateSecurityGroup.id;

// Export database connection info
export const postgresEndpoint = postgres.endpoint;
export const postgresPort = postgres.port;
export const postgresDbName = postgres.dbName;
export const postgresUsername = "postgres";
export const postgresPassword = dbPassword;

// Export cache connection info
export const redisEndpoint = redis.configurationEndpointAddress;
export const redisPort = redis.port;

// Export ECR repository information
export const ecrRepositoryUrl = ecrRepository.repositoryUrl;
export const ecrRepositoryName = ecrRepository.name;
export const ecrRegistryId = ecrRepository.registryId;

// Export Bastion host information for development access
export const bastionInstanceId = bastionHost.id;
export const bastionPublicIp = bastionHost.publicIp;
export const bastionSecurityGroupId = bastionSecurityGroup.id;

// Export project configuration for .env.local generation
export const envConfig = pulumi.jsonStringify({
	DATABASE_URL: pulumi.interpolate`postgresql://postgres:${dbPassword}@${postgres.endpoint}:${postgres.port}/maindb`,
	REDIS_URL: pulumi.interpolate`redis://${redis.configurationEndpointAddress}:${redis.port}`,
	PROJECT_NAME: projectName,
	S3_BUCKET_NAME: bucket.id,
	CDN_URL: pulumi.interpolate`https://${distribution.domainName}`,
	CDN_DISTRIBUTION_ID: distribution.id,
});

// Export Fargate-specific configuration
export const fargateConfig = pulumi.jsonStringify({
	VPC_ID: vpc.vpcId,
	PUBLIC_SUBNET_IDS: vpc.publicSubnetIds,
	PRIVATE_SUBNET_IDS: vpc.privateSubnetIds,
	DB_SECURITY_GROUP_ID: dbSecurityGroup.id,
	CACHE_SECURITY_GROUP_ID: cacheSecurityGroup.id,
	FARGATE_SECURITY_GROUP_ID: fargateSecurityGroup.id,
	BASTION_SECURITY_GROUP_ID: bastionSecurityGroup.id,
	DB_ENDPOINT: postgres.endpoint,
	DB_PORT: postgres.port,
	REDIS_ENDPOINT: redis.configurationEndpointAddress,
	REDIS_PORT: redis.port,
	ECR_REPOSITORY_URL: ecrRepository.repositoryUrl,
	ECR_REPOSITORY_NAME: ecrRepository.name,
	BASTION_INSTANCE_ID: bastionHost.id,
});
