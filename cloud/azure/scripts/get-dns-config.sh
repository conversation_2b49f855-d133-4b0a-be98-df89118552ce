#!/bin/bash

# Get DNS configuration for whiteboardanimation.ai domain
# Usage: ./scripts/get-dns-config.sh

set -e

echo "🔍 获取 Azure Container Apps 域名配置信息..."
echo ""

# Check if we're in the right directory
if [ ! -f "Pulumi.yaml" ]; then
    echo "❌ 错误: 请在 cloud/azure 目录中运行此脚本"
    exit 1
fi

# Get Pulumi outputs
echo "📋 从 Pulumi 获取基础信息..."
RESOURCE_GROUP=$(pulumi stack output resourceGroupName 2>/dev/null)
ENVIRONMENT_NAME=$(pulumi stack output containerAppsEnvironmentName 2>/dev/null)
STATIC_IP=$(pulumi stack output containerAppsEnvironmentStaticIp 2>/dev/null)
DEFAULT_DOMAIN=$(pulumi stack output containerAppsEnvironmentDefaultDomain 2>/dev/null)

if [ -z "$RESOURCE_GROUP" ] || [ -z "$ENVIRONMENT_NAME" ]; then
    echo "❌ 错误: 无法获取 Pulumi 输出，请确保基础设施已部署"
    exit 1
fi

echo "✅ 资源组: $RESOURCE_GROUP"
echo "✅ 环境名称: $ENVIRONMENT_NAME"
echo "✅ 静态 IP: $STATIC_IP"
echo "✅ 默认域名: $DEFAULT_DOMAIN"
echo ""

# Get domain verification ID from Azure CLI
echo "🔍 从 Azure CLI 获取域名验证 ID..."
VERIFICATION_ID=$(az containerapp env show \
    --name "$ENVIRONMENT_NAME" \
    --resource-group "$RESOURCE_GROUP" \
    --query "properties.customDomainVerificationId" \
    --output tsv 2>/dev/null)

if [ -z "$VERIFICATION_ID" ] || [ "$VERIFICATION_ID" = "null" ]; then
    echo "⚠️  警告: 无法获取域名验证 ID，可能需要先配置自定义域名"
    VERIFICATION_ID="<需要通过 Azure Portal 获取>"
fi

echo "✅ 域名验证 ID: $VERIFICATION_ID"
echo ""

# Display DNS configuration
echo "🌐 Cloudflare DNS 配置信息"
echo "=================================="
echo ""

echo "📍 A 记录配置:"
echo "类型: A"
echo "名称: *"
echo "内容: $STATIC_IP"
echo "代理状态: Proxied (橙色云朵) ✅"
echo "TTL: Auto"
echo ""

echo "📍 TXT 记录配置 (域名验证):"
echo "类型: TXT"
echo "名称: asuid"
echo "内容: $VERIFICATION_ID"
echo "代理状态: DNS only (灰色云朵)"
echo "TTL: Auto"
echo ""

# Display verification commands
echo "🔍 验证命令"
echo "=================================="
echo ""

echo "检查 A 记录:"
echo "dig @******* test.whiteboardanimation.ai A"
echo ""

echo "检查 TXT 记录:"
echo "dig @******* asuid.whiteboardanimation.ai TXT"
echo ""

echo "测试 HTTPS 访问:"
echo "curl -I https://test.whiteboardanimation.ai"
echo ""

# Display summary
echo "📋 配置总结"
echo "=================================="
echo "1. 在 Cloudflare 中添加上述 A 记录和 TXT 记录"
echo "2. 等待 DNS 传播 (5-10 分钟)"
echo "3. 重新部署 Azure 基础设施: pulumi up"
echo "4. 测试域名解析和 HTTPS 访问"
echo ""

echo "🎯 完成后，你的应用将自动获得:"
echo "   https://<app-name>.whiteboardanimation.ai"
echo ""

# Save to file
CONFIG_FILE="dns-config.txt"
cat > "$CONFIG_FILE" << EOF
# Cloudflare DNS 配置 - whiteboardanimation.ai
# 生成时间: $(date)

## A 记录 (通配符)
类型: A
名称: *
内容: $STATIC_IP
代理状态: Proxied (橙色云朵)

## TXT 记录 (域名验证)
类型: TXT
名称: asuid
内容: $VERIFICATION_ID
代理状态: DNS only (灰色云朵)

## 验证命令
dig @******* test.whiteboardanimation.ai A
dig @******* asuid.whiteboardanimation.ai TXT
curl -I https://test.whiteboardanimation.ai

## Azure 资源信息
资源组: $RESOURCE_GROUP
环境名称: $ENVIRONMENT_NAME
静态 IP: $STATIC_IP
默认域名: $DEFAULT_DOMAIN
验证 ID: $VERIFICATION_ID
EOF

echo "💾 配置信息已保存到: $CONFIG_FILE"
