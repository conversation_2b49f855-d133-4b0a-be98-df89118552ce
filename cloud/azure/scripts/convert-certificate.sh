#!/bin/bash

# Convert Cloudflare Origin Certificate to PFX format and encode to base64
# Usage: ./convert-certificate.sh <cert.pem> <private-key.pem> <output-password>

set -e

if [ $# -ne 3 ]; then
    echo "Usage: $0 <certificate.pem> <private-key.pem> <pfx-password>"
    echo ""
    echo "Example:"
    echo "  $0 origin-cert.pem origin-key.pem mySecurePassword123"
    echo ""
    echo "This script will:"
    echo "  1. Combine your PEM certificate and private key into a PFX file"
    echo "  2. Encode the PFX file to base64"
    echo "  3. Output the base64 string for Pulumi configuration"
    exit 1
fi

CERT_FILE="$1"
KEY_FILE="$2"
PFX_PASSWORD="$3"
PFX_FILE="wildcard-a1d-ai.pfx"

# Check if input files exist
if [ ! -f "$CERT_FILE" ]; then
    echo "Error: Certificate file '$CERT_FILE' not found!"
    exit 1
fi

if [ ! -f "$KEY_FILE" ]; then
    echo "Error: Private key file '$KEY_FILE' not found!"
    exit 1
fi

echo "🔧 Converting PEM to PFX..."

# Convert PEM to PFX using openssl
openssl pkcs12 -export \
    -out "$PFX_FILE" \
    -inkey "$KEY_FILE" \
    -in "$CERT_FILE" \
    -password "pass:$PFX_PASSWORD"

if [ $? -eq 0 ]; then
    echo "✅ PFX file created: $PFX_FILE"
else
    echo "❌ Failed to create PFX file"
    exit 1
fi

echo "📦 Encoding PFX to Base64..."

# Encode PFX to base64
BASE64_OUTPUT=$(base64 -i "$PFX_FILE")

echo ""
echo "🎉 Conversion completed successfully!"
echo ""
echo "📋 Use these commands to configure Pulumi:"
echo ""
echo "pulumi config set --secret originPfxBase64 \"$BASE64_OUTPUT\""
echo "pulumi config set --secret originPfxPassword \"$PFX_PASSWORD\""
echo ""
echo "🗑️  Clean up temporary files:"
echo "rm $PFX_FILE"
echo ""

# Clean up the PFX file (keep only if user wants it)
read -p "Delete temporary PFX file '$PFX_FILE'? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm "$PFX_FILE"
    echo "🗑️  Deleted $PFX_FILE"
else
    echo "�� Kept $PFX_FILE"
fi