# 域名验证指南 - TXT 记录配置

本指南详细说明如何获取和配置 TXT 记录来验证 `whiteboardanimation.ai` 域名所有权。

## 🎯 目标

为 Azure Container Apps Environment 验证自定义域名 `whiteboardanimation.ai`，以便：
- 使用环境级别的自定义 DNS 后缀
- 自动为所有应用分配 `<app-name>.whiteboardanimation.ai` 域名
- 启用 HTTPS 加密

## 📋 前提条件

1. ✅ Azure 基础设施已部署
2. ✅ 有 Cloudflare 账户访问权限
3. ✅ `whiteboardanimation.ai` 域名已托管在 Cloudflare

## 🔍 方法 1: 通过 Pulumi 输出获取验证信息

### 1.1 获取域名验证 ID

```bash
cd cloud/azure

# 获取 Container Apps Environment 的默认域名
pulumi stack output containerAppsEnvironmentDomainVerificationId
```

输出示例：
```
proudpond-12345678.eastasia.azurecontainerapps.io
```

### 1.2 构造 TXT 记录

基于输出的默认域名，你需要添加以下 TXT 记录：

```
类型: TXT
名称: asuid.whiteboardanimation.ai
值: [从默认域名提取的验证 ID]
```

## 🔍 方法 2: 通过 Azure Portal 获取

### 2.1 登录 Azure Portal

```
https://portal.azure.com
```

### 2.2 导航到 Container Apps Environment

```
资源组 → [你的资源组名] → Container Apps Environment → [环境名称]
```

### 2.3 查看自定义域名配置

```
设置 → 自定义域名 → 查看验证要求
```

Azure 会显示需要的 TXT 记录信息。

## 🔍 方法 3: 通过 Azure CLI 获取

```bash
# 获取资源组名称
RESOURCE_GROUP=$(pulumi stack output resourceGroupName)

# 获取环境名称
ENVIRONMENT_NAME=$(pulumi stack output containerAppsEnvironmentName)

# 获取域名验证信息
az containerapp env show \
  --name $ENVIRONMENT_NAME \
  --resource-group $RESOURCE_GROUP \
  --query "properties.defaultDomain" \
  --output tsv
```

## 🌐 在 Cloudflare 中配置 TXT 记录

### 3.1 登录 Cloudflare Dashboard

```
https://dash.cloudflare.com
选择域名: whiteboardanimation.ai
```

### 3.2 添加 TXT 记录

导航到：`DNS → Records → Add record`

配置如下：
```
类型: TXT
名称: asuid
内容: [Azure 提供的验证 ID]
代理状态: DNS only (灰色云朵)
TTL: Auto
```

### 3.3 验证 DNS 传播

```bash
# 检查 TXT 记录是否生效
dig TXT asuid.whiteboardanimation.ai

# 或使用在线工具
# https://toolbox.googleapps.com/apps/dig/#TXT/asuid.whiteboardanimation.ai
```

## 🚀 完成域名验证

### 4.1 重新部署基础设施

```bash
cd cloud/azure

# 重新部署以应用域名配置
pulumi up
```

### 4.2 验证配置成功

```bash
# 检查环境状态
az containerapp env show \
  --name $ENVIRONMENT_NAME \
  --resource-group $RESOURCE_GROUP \
  --query "properties.customDomainConfiguration"
```

### 4.3 测试域名解析

```bash
# 获取静态 IP
STATIC_IP=$(pulumi stack output containerAppsEnvironmentStaticIp)

# 检查域名解析
dig @******* test.whiteboardanimation.ai A

# 应该返回你的静态 IP
```

## 📊 配置 A 记录

域名验证完成后，还需要配置 A 记录：

### 在 Cloudflare 中添加 A 记录

```
类型: A
名称: *
内容: [你的 Azure Container Apps 静态 IP]
代理状态: Proxied (橙色云朵) ✅
TTL: Auto
```

获取静态 IP：
```bash
pulumi stack output containerAppsEnvironmentStaticIp
```

## 🔍 故障排除

### 问题 1: TXT 记录未生效

**检查方法**:
```bash
# 检查 DNS 传播
dig TXT asuid.whiteboardanimation.ai @*******
dig TXT asuid.whiteboardanimation.ai @*******
```

**解决方案**:
- 等待 DNS 传播（通常 5-10 分钟）
- 检查记录名称是否正确（`asuid`）
- 确认代理状态为 "DNS only"

### 问题 2: 域名验证失败

**检查方法**:
```bash
# 检查 Azure 环境状态
az containerapp env show \
  --name $ENVIRONMENT_NAME \
  --resource-group $RESOURCE_GROUP \
  --query "properties.provisioningState"
```

**解决方案**:
- 确认 TXT 记录值正确
- 重新部署基础设施
- 检查域名是否已在其他地方使用

### 问题 3: 应用无法访问

**检查方法**:
```bash
# 测试域名解析
curl -I https://test.whiteboardanimation.ai

# 检查证书
openssl s_client -connect test.whiteboardanimation.ai:443 -servername test.whiteboardanimation.ai
```

**解决方案**:
- 确认 A 记录指向正确的静态 IP
- 检查 Cloudflare 代理状态
- 验证证书配置

## 📚 相关文档

- [Cloudflare Origin Certificate 设置](./cloudflare-origin-certificate-setup.md)
- [Azure Container Apps 自定义域名文档](https://docs.microsoft.com/en-us/azure/container-apps/custom-domains-certificates)
- [DNS 记录类型说明](https://developers.cloudflare.com/dns/manage-dns-records/reference/dns-record-types/)

## ✅ 验证清单

完成以下步骤确保配置正确：

- [ ] 获取了 Azure 域名验证 ID
- [ ] 在 Cloudflare 中添加了 TXT 记录
- [ ] TXT 记录已生效（DNS 查询成功）
- [ ] 重新部署了 Azure 基础设施
- [ ] 配置了通配符 A 记录
- [ ] 测试了域名解析和 HTTPS 访问

完成这些步骤后，你的应用将自动获得 `<app-name>.whiteboardanimation.ai` 域名！
