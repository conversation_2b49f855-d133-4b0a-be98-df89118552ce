# Cloudflare Origin Certificate 配置指南

本指南详细说明如何获取和配置 Cloudflare Origin Certificate，用于 Azure Container Apps 的 HTTPS 加密。

## 🎯 目标

在 Azure Container Apps 中使用 Cloudflare 的免费 15 年 Origin Certificate，实现：
- Cloudflare ↔ Azure Container Apps 之间的端到端加密
- 支持通配符域名 `*.a1d.ai`
- 无需额外证书费用

## 📋 前提条件

1. ✅ 域名 `a1d.ai` 已托管在 Cloudflare
2. ✅ 有 Cloudflare 账户访问权限
3. ✅ 本地安装了 `openssl` 工具

## 🔧 步骤一：在 Cloudflare Dashboard 创建 Origin Certificate

### 1.1 登录 Cloudflare Dashboard
```
访问：https://dash.cloudflare.com
选择域名：a1d.ai
```

### 1.2 导航到 Origin Certificate 创建页面
```
路径：SSL/TLS → Origin Server → Create Certificate
```

### 1.3 配置证书选项
- **生成方式**: "Generate private key and CSR with Cloudflare" ✅
- **私钥类型**: RSA (推荐)
- **主机名**: 确保包含
  - `a1d.ai` ✅
  - `*.a1d.ai` ✅
- **证书有效期**: 15 years (最长期限) ✅

### 1.4 下载证书文件
1. 点击 "Create" 按钮
2. **Key Format**: 选择 **PEM (Base64-encoded)**
3. 保存两个文件：
   - `origin-cert.pem` - Origin Certificate
   - `origin-key.pem` - Private Key

⚠️ **重要**: 私钥只显示一次，请立即保存！

## 🔄 步骤二：转换证书格式并编码

### 2.1 使用提供的转换脚本

```bash
# 在项目根目录执行
cd cloud/azure

# 运行转换脚本
./scripts/convert-certificate.sh secret/origin-cert.pem secret/origin-key.pem password_a1d_ai
```

### 2.2 手动转换 (可选)

如果不使用脚本，可以手动执行：

```bash
# 1. 合并证书和私钥为 PFX 格式
openssl pkcs12 -export \
    -out wildcard-a1d-ai.pfx \
    -inkey origin-key.pem \
    -in origin-cert.pem \
    -password pass:your_secure_password_123

# 2. 转换为 Base64 编码
base64 -i wildcard-a1d-ai.pfx
```

## ⚙️ 步骤三：配置 Pulumi

### 3.1 设置 Pulumi 配置

使用脚本输出的命令，或手动执行：

```bash
# 设置证书的 Base64 编码 (替换为实际的 base64 字符串)
pulumi config set --secret originPfxBase64 "MIIKs...很长的base64字符串...=="

# 设置 PFX 密码
pulumi config set --secret originPfxPassword "your_secure_password_123"
```

### 3.2 验证配置

```bash
# 检查配置是否正确设置
pulumi config
```

应该看到类似输出：
```
KEY                VALUE
originPfxBase64    [secret]
originPfxPassword  [secret]
projectName        a1d-azure
```

## 🚀 步骤四：部署基础设施

```bash
# 重新部署以包含证书
pnpm run up
```

成功后，您将看到证书相关的输出：
```
certificateInfo: {
    certificateId: "/subscriptions/.../certificates/..."
    certificateName: "a1d-azure-wildcard-cert"
    domain: "*.a1d.ai"
    provider: "Cloudflare Origin CA (15 years)"
    keyVaultSecret: "wildcard-a1d-ai-certificate"
    usage: "Reference certificateId when creating Container Apps with custom domains"
}
```

## 📝 步骤五：DNS 配置

### 5.1 配置 A 记录

在 Cloudflare DNS 中添加：
```
Type: A
Name: *
Content: *********** (您的 Azure Container Apps 静态IP)
Proxy status: Proxied (橙色云朵) ✅
```

### 5.2 验证配置

```bash
# 检查 DNS 解析
dig @******* api.a1d.ai A
dig @******* app.a1d.ai A
```

## 🎯 使用证书

在创建 Container Apps 时，引用证书：

```typescript
const containerApp = new azure.app.ContainerApp("my-app", {
    // ... 其他配置
    configuration: {
        ingress: {
            external: true,
            targetPort: 3000,
            customDomains: [{
                name: "api.a1d.ai",
                certificateId: wildcardCertificateId, // 使用导出的证书ID
                bindingType: azure.app.BindingType.SniEnabled,
            }],
        },
    },
});
```

## 🔍 故障排除

### 问题：证书转换失败
```bash
# 检查 OpenSSL 版本
openssl version

# 验证 PEM 文件格式
openssl x509 -in origin-cert.pem -text -noout
openssl rsa -in origin-key.pem -check
```

### 问题：Base64 编码错误
```bash
# macOS 使用
base64 -i file.pfx

# Linux 使用
base64 file.pfx
```

### 问题：Pulumi 配置不生效
```bash
# 重新设置配置
pulumi config rm originPfxBase64
pulumi config rm originPfxPassword

# 重新设置
pulumi config set --secret originPfxBase64 "..."
pulumi config set --secret originPfxPassword "..."
```

## 🔒 安全注意事项

1. **私钥安全**: 不要将私钥提交到版本控制系统
2. **密码强度**: 使用强密码保护 PFX 文件
3. **访问限制**: 限制对 Key Vault 的访问权限
4. **证书轮换**: 虽然有效期15年，建议定期轮换证书

## 📚 相关资源

- [Cloudflare Origin CA 文档](https://developers.cloudflare.com/ssl/origin-configuration/origin-ca)
- [Azure Container Apps 自定义域名](https://docs.microsoft.com/en-us/azure/container-apps/custom-domains-certificates)
- [OpenSSL PKCS12 文档](https://www.openssl.org/docs/man1.1.1/man1/openssl-pkcs12.html)