# Azure 全栈部署指南

本指南详细介绍如何在 Azure 上部署完整的应用基础设施，包括数据库、缓存、存储、CDN 和容器服务。

## 🏗️ 架构概览

### Azure 服务架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Azure 基础设施                              │
├─────────────────────────────────────────────────────────────┤
│  🌐 Azure CDN                                               │
│  ├── 全球内容分发                                             │
│  └── 静态资源缓存                                             │
├─────────────────────────────────────────────────────────────┤
│  📦 Azure Blob Storage                                      │
│  ├── 静态文件存储                                             │
│  └── 媒体资源存储                                             │
├─────────────────────────────────────────────────────────────┤
│  🐳 Azure Container Registry + Container Apps               │
│  ├── 容器镜像存储                                             │
│  └── 应用容器运行                                             │
├─────────────────────────────────────────────────────────────┤
│  🌐 Azure Virtual Network (VNet)                           │
│  ├── 公有子网 (********/24)                                 │
│  │   └── Container Apps                                    │
│  └── 私有子网 (********/24)                                 │
│      ├── 🗄️ Azure Database for PostgreSQL                  │
│      └── 🚀 Azure Cache for Redis                          │
└─────────────────────────────────────────────────────────────┘
```

### 与 AWS 对比

| 功能 | AWS | Azure |
|------|-----|-------|
| 对象存储 | S3 | Blob Storage |
| CDN | CloudFront | Azure CDN |
| 容器注册表 | ECR | Container Registry |
| 容器服务 | Fargate | Container Apps |
| 数据库 | RDS PostgreSQL | Database for PostgreSQL |
| 缓存 | ElastiCache Redis | Cache for Redis |
| 网络 | VPC | Virtual Network |
| 安全组 | Security Groups | Network Security Groups |

## 🚀 快速部署

### 前置要求

1. **Azure 账户和认证**
   - 参考：[Azure 认证配置指南](./azure-auth-setup.md)

2. **工具安装**
   ```bash
   # Azure CLI
   brew install azure-cli  # macOS
   
   # Pulumi CLI
   brew install pulumi     # macOS
   
   # Node.js 和 pnpm
   brew install node pnpm  # macOS
   ```

### 1. 克隆和配置项目

```bash
# 克隆项目
git clone <repository-url>
cd a1d-pulumi

# 切换到 Azure 分支
git checkout azure-environment

# 安装依赖
pnpm install
```

### 2. Azure 认证

```bash
# 登录 Azure
az login

# 验证登录
az account show
```

### 3. Pulumi 配置

```bash
# 创建新的 Azure stack
pulumi stack init azure-dev

# 设置基本配置
pulumi config set azure-native:location "East Asia"
pulumi config set projectName "a1d-azure"

# 设置数据库密码（安全存储）
pulumi config set --secret dbPassword "your-secure-password"
```

### 4. 部署基础设施

```bash
# 预览部署计划
pulumi preview -f Pulumi.azure.yaml

# 执行部署
pulumi up -f Pulumi.azure.yaml
```

### 5. 生成环境配置

```bash
# 生成 .env.azure 文件
bun run scripts/azure/generate-env.ts

# 查看生成的配置
cat .env.azure
```

## 📊 资源详情

### 核心资源

1. **Resource Group**
   - 名称：`{projectName}-rg`
   - 位置：East Asia
   - 用途：所有资源的容器

2. **Virtual Network**
   - CIDR：10.0.0.0/16
   - 公有子网：********/24
   - 私有子网：********/24

3. **Storage Account**
   - SKU：Standard_LRS
   - 类型：StorageV2
   - 访问层：Hot

4. **Azure Database for PostgreSQL**
   - 版本：PostgreSQL 17
   - SKU：Standard_B1ms (ARM-based)
   - 存储：32GB
   - 网络：私有子网

5. **Azure Cache for Redis**
   - 版本：Redis 6
   - SKU：Basic C0
   - TLS：1.2+
   - 网络：私有子网

6. **Container Registry**
   - SKU：Basic
   - 管理员用户：启用

7. **CDN Profile & Endpoint**
   - SKU：Standard Microsoft
   - 源：Blob Storage

### 网络安全

- **数据库 NSG**：仅允许 VNet 内 5432 端口访问
- **Redis NSG**：仅允许 VNet 内 6379 端口访问
- **私有子网**：数据库和缓存隔离
- **公有子网**：应用容器访问

## 🔧 本地开发

### 环境变量配置

生成的 `.env.azure` 包含：

```env
# Platform Configuration
PLATFORM=azure
PROJECT_NAME=a1d-azure

# Database Configuration
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/maindb?sslmode=require
DB_HOST=server.postgres.database.azure.com
DB_PORT=5432
DB_NAME=maindb
DB_USER=postgres

# Redis Configuration
REDIS_URL=rediss://:<EMAIL>:6380
REDIS_HOST=cache.redis.cache.windows.net
REDIS_PORT=6380

# Storage Configuration
STORAGE_ACCOUNT_NAME=storageaccount
STORAGE_CONTAINER_NAME=static
CDN_URL=https://endpoint.azureedge.net

# Container Registry
ACR_LOGIN_SERVER=registry.azurecr.io
```

### 数据库连接

Azure Database for PostgreSQL 要求 SSL 连接：

```typescript
// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  ssl: {
    rejectUnauthorized: false, // Azure 证书
  },
};
```

### Redis 连接

Azure Cache for Redis 使用 SSL 端口：

```typescript
// Redis 连接配置
import { createClient } from 'redis';

const redisClient = createClient({
  url: process.env.REDIS_URL, // rediss:// 协议
  socket: {
    tls: true,
    rejectUnauthorized: false,
  },
});
```

## 🐳 容器部署

### 1. 构建和推送镜像

```bash
# 登录 ACR
az acr login --name {acrName}

# 构建镜像
docker build -t {acrLoginServer}/app:latest .

# 推送镜像
docker push {acrLoginServer}/app:latest
```

### 2. 创建 Container App

```typescript
// 在 Pulumi 中创建 Container App
const containerApp = new azure.app.ContainerApp("app", {
  resourceGroupName: resourceGroup.name,
  managedEnvironmentId: containerEnv.id,
  configuration: {
    ingress: {
      external: true,
      targetPort: 3000,
    },
    registries: [{
      server: containerRegistry.loginServer,
      username: containerRegistry.adminUsername,
      passwordSecretRef: "registry-password",
    }],
    secrets: [{
      name: "registry-password",
      value: containerRegistry.adminPassword,
    }],
  },
  template: {
    containers: [{
      name: "app",
      image: pulumi.interpolate`${containerRegistry.loginServer}/app:latest`,
      resources: {
        cpu: 0.25,
        memory: "0.5Gi",
      },
      env: [
        { name: "DATABASE_URL", value: envConfig.DATABASE_URL },
        { name: "REDIS_URL", value: envConfig.REDIS_URL },
      ],
    }],
  },
});
```

## 💰 成本优化

### 推荐配置

1. **开发环境**
   - PostgreSQL：Standard_B1ms (ARM-based)
   - Redis：Basic C0
   - Storage：Standard_LRS
   - CDN：Standard Microsoft

2. **生产环境**
   - PostgreSQL：Standard_D2s_v3
   - Redis：Standard C1
   - Storage：Standard_GRS
   - CDN：Premium Verizon

### 成本估算

| 服务 | 开发环境 | 生产环境 |
|------|----------|----------|
| PostgreSQL | ~$15/月 | ~$100/月 |
| Redis | ~$15/月 | ~$75/月 |
| Storage | ~$5/月 | ~$20/月 |
| CDN | ~$5/月 | ~$50/月 |
| Container Apps | ~$10/月 | ~$100/月 |
| **总计** | **~$50/月** | **~$345/月** |

## 🔍 监控和维护

### Azure Monitor

```bash
# 启用诊断设置
az monitor diagnostic-settings create \
  --resource {resourceId} \
  --name "default" \
  --logs '[{"category":"PostgreSQLLogs","enabled":true}]' \
  --metrics '[{"category":"AllMetrics","enabled":true}]' \
  --workspace {logAnalyticsWorkspaceId}
```

### 关键指标

- **数据库**：CPU、内存、连接数、查询性能
- **Redis**：内存使用、命中率、连接数
- **Storage**：请求数、延迟、可用性
- **CDN**：请求数、缓存命中率、带宽
- **Container Apps**：CPU、内存、请求数、响应时间

## 🚨 故障排查

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查防火墙规则
   az postgres flexible-server firewall-rule list --resource-group {rg} --name {server}
   
   # 添加客户端 IP
   az postgres flexible-server firewall-rule create \
     --resource-group {rg} --name {server} \
     --rule-name "AllowMyIP" \
     --start-ip-address {yourIP} \
     --end-ip-address {yourIP}
   ```

2. **Redis 连接超时**
   ```bash
   # 检查 Redis 状态
   az redis show --resource-group {rg} --name {redisName}
   
   # 重启 Redis
   az redis force-reboot --resource-group {rg} --name {redisName} --reboot-type AllNodes
   ```

3. **Container App 部署失败**
   ```bash
   # 查看部署日志
   az containerapp logs show --name {appName} --resource-group {rg}
   
   # 检查镜像拉取权限
   az acr repository show --name {acrName} --image {imageName}
   ```

## 📚 相关文档

- [Azure 认证配置指南](./azure-auth-setup.md)
- [Azure Container Apps 指南](./azure-container-apps-guide.md)
- [Azure 成本优化指南](./azure-cost-optimization.md)
- [Azure 监控配置指南](./azure-monitoring-guide.md)
