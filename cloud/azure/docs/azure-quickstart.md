# Azure 快速开始指南

本指南将帮助你在 5 分钟内快速部署 Azure 基础设施。

## 🚀 5 分钟快速部署

### 前置要求

- Azure 账户（免费账户即可）
- 已安装 Azure CLI 和 Pulumi CLI

### 1. 认证配置

```bash
# 登录 Azure
az login

# 验证登录
az account show
```

### 2. 项目设置

```bash
# 克隆项目
git clone <repository-url>
cd a1d-pulumi

# 切换到 Azure 分支
git checkout azure-environment

# 安装依赖
pnpm install
```

### 3. 快速测试部署

```bash
# 创建测试 stack
pulumi stack init azure-test

# 设置基本配置
pulumi config set azure-native:location "East Asia"
pulumi config set projectName "test-azure"

# 部署最小配置（仅创建资源组和存储账户）
pnpm run azure:test
```

### 4. 完整基础设施部署

```bash
# 创建生产 stack
pulumi stack init azure-dev

# 设置完整配置
pulumi config set azure-native:location "East Asia"
pulumi config set projectName "a1d-azure"
pulumi config set --secret dbPassword "your-secure-password"

# 部署完整基础设施
pnpm run azure:up
```

### 5. 生成环境配置

```bash
# 生成 .env.azure 文件
pnpm run azure:env:gen

# 查看生成的配置
cat .env.azure
```

## 📋 部署内容

### 最小测试部署 (`azure:test`)

- ✅ Resource Group
- ✅ Storage Account
- ✅ 基础标签

**用途**：验证 Azure 连接和权限

### 完整基础设施部署 (`azure:up`)

- ✅ Resource Group
- ✅ Virtual Network + 子网
- ✅ Storage Account + Blob Container
- ✅ Azure CDN
- ✅ PostgreSQL Database
- ✅ Redis Cache
- ✅ Container Registry
- ✅ Network Security Groups

**用途**：生产级基础设施

## 🔧 常用命令

```bash
# Azure 相关命令
pnpm run azure:preview      # 预览 Azure 部署
pnpm run azure:up          # 部署 Azure 基础设施
pnpm run azure:env:gen     # 生成环境变量
pnpm run azure:destroy     # 销毁 Azure 资源

# 测试相关命令
pnpm run azure:test:preview # 预览测试部署
pnpm run azure:test        # 部署测试资源
pnpm run azure:test:destroy # 销毁测试资源

# Stack 管理
pulumi stack ls            # 列出所有 stack
pulumi stack select azure-dev  # 切换到指定 stack
pulumi stack output        # 查看输出
```

## 🌍 区域选择

推荐的 Azure 区域：

| 区域 | 英文名称 | 延迟 | 推荐度 |
|------|----------|------|--------|
| 香港 | East Asia | 最低 | ⭐⭐⭐⭐⭐ |
| 新加坡 | Southeast Asia | 低 | ⭐⭐⭐⭐ |
| 东京 | Japan East | 中等 | ⭐⭐⭐ |

```bash
# 设置区域
pulumi config set azure-native:location "East Asia"
```

## 💰 成本估算

### 测试环境 (最小配置)

- Storage Account: ~$2/月
- **总计**: ~$2/月

### 开发环境 (完整配置)

- PostgreSQL: ~$15/月
- Redis: ~$15/月
- Storage + CDN: ~$10/月
- Container Registry: ~$5/月
- Network: ~$5/月
- **总计**: ~$50/月

## 🔍 验证部署

### 1. 检查资源组

```bash
# 列出资源组
az group list --output table

# 查看资源组详情
az group show --name {resourceGroupName}
```

### 2. 检查存储账户

```bash
# 列出存储账户
az storage account list --output table

# 测试存储连接
az storage blob list --account-name {storageAccountName} --container-name static
```

### 3. 检查数据库（完整部署）

```bash
# 列出 PostgreSQL 服务器
az postgres flexible-server list --output table

# 测试数据库连接
psql "postgresql://postgres:<EMAIL>:5432/maindb?sslmode=require"
```

## 🚨 故障排查

### 常见问题

1. **权限不足**
   ```
   ERROR: (AuthorizationFailed) The client does not have authorization
   ```
   **解决**: 确保账户有 Contributor 权限

2. **区域不支持**
   ```
   ERROR: The subscription is not registered to use namespace
   ```
   **解决**: 注册资源提供程序
   ```bash
   az provider register --namespace Microsoft.Storage
   az provider register --namespace Microsoft.Network
   ```

3. **名称冲突**
   ```
   ERROR: Storage account name must be unique
   ```
   **解决**: 修改项目名称
   ```bash
   pulumi config set projectName "unique-name"
   ```

## 🎯 下一步

1. **开发应用**: 使用生成的 `.env.azure` 配置
2. **容器部署**: 参考 [Azure Container Apps 指南](./azure-container-apps-guide.md)
3. **监控设置**: 参考 [Azure 监控指南](./azure-monitoring-guide.md)
4. **成本优化**: 参考 [Azure 成本优化指南](./azure-cost-optimization.md)

## 📚 相关文档

- [Azure 认证配置指南](./azure-auth-setup.md)
- [Azure 完整部署指南](./azure-deployment-guide.md)
- [AWS vs Azure 对比](./aws-azure-comparison.md)
