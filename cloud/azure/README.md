# Azure 基础设施

基于 Pulumi 的 Azure Container Apps 基础设施，集成 Cloudflare 证书管理，为 a1d 项目提供完整的云原生解决方案。

## 🏗️ 架构概览

本项目创建以下 Azure 资源：

- **网络基础设施**: VNet、公有/私有子网、网络安全组
- **容器平台**: Container Apps Environment 与静态 IP
- **证书管理**: Cloudflare Origin CA 通配符证书 (*.azure.a1d.ai)
- **容器注册表**: Azure Container Registry (ACR)
- **安全存储**: Key Vault 用于密钥管理
- **监控日志**: Log Analytics Workspace

## 🚀 快速开始

### 前置要求

1. **Azure CLI**: `az login` 完成认证
2. **Pulumi CLI**: 已安装并配置
3. **Node.js 18+**: 支持 TypeScript
4. **pnpm**: 包管理器

### 安装依赖

```bash
cd cloud/azure
pnpm install
```

### 基础配置

```bash
# 创建新的 Pulumi stack
pulumi stack init azure-dev

# 设置 Azure 区域
pulumi config set azure-native:location "East Asia"

# 设置项目名称（可选）
pulumi config set projectName "a1d-azure"
```

### 证书配置

获取 Cloudflare Origin Certificate 并配置：

```bash
# 设置 PFX 证书（Base64 编码）
pulumi config set --secret originPfxBase64 "<base64-encoded-pfx-file>"

# 设置证书密码
pulumi config set --secret originPfxPassword "<pfx-password>"
```

> 📋 详细的证书获取步骤请参考 [Cloudflare Origin Certificate 设置指南](./docs/cloudflare-origin-certificate-setup.md)

### 部署基础设施

```bash
# 预览部署计划
pnpm run preview

# 执行部署
pnpm run up

# 查看输出
pulumi stack output
```

## 📊 输出信息

部署完成后，你将获得以下关键输出：

```bash
# 容器应用环境
containerAppsEnvironmentId: /subscriptions/.../managedEnvironments/...
containerAppsEnvironmentStaticIp: "20.xxx.xxx.xxx"

# 容器注册表
acrLoginServer: "a1dazureacr.azurecr.io"

# 证书信息
wildcardCertificateId: /subscriptions/.../certificates/...
wildcardDomain: "*.azure.a1d.ai"

# 网络配置
vnetCidrBlock: "10.0.0.0/16"
publicSubnetId: /subscriptions/.../subnets/public
```

## 🛠️ 可用脚本

| 脚本 | 描述 |
|------|------|
| `pnpm run up` | 部署基础设施 |
| `pnpm run preview` | 预览变更 |
| `pnpm run destroy` | 销毁资源 |
| `pnpm run refresh` | 刷新状态 |
| `pnpm run env:gen` | 生成环境变量文件 |

## 📁 项目结构

```
cloud/azure/
├── src/
│   ├── index.ts              # 主入口文件
│   ├── index-minimal.ts      # 最小化配置
│   ├── lib/                  # 共享库
│   ├── modules/              # 可选模块
│   └── examples/             # 示例配置
├── scripts/
│   ├── generate-env.ts       # 环境变量生成
│   ├── convert-certificate.sh # 证书转换工具
│   └── utils.ts              # 工具函数
├── docs/                     # 详细文档
│   ├── azure-quickstart.md
│   ├── azure-auth-setup.md
│   ├── azure-deployment-guide.md
│   └── cloudflare-origin-certificate-setup.md
├── package.json
├── Pulumi.yaml
└── tsconfig.json
```

## 🔧 配置选项

### 必需配置

```bash
# Azure 区域
pulumi config set azure-native:location "East Asia"

# Cloudflare Origin Certificate
pulumi config set --secret originPfxBase64 "<base64-pfx>"
pulumi config set --secret originPfxPassword "<password>"
```

### 可选配置

```bash
# 项目名称（默认: a1d-azure）
pulumi config set projectName "my-project"

# 自定义位置（默认: East Asia）
pulumi config set location "Southeast Asia"
```

## 🌐 DNS 配置

部署完成后，需要配置 DNS 记录：

1. 获取静态 IP: `pulumi stack output containerAppsEnvironmentStaticIp`
2. 在 Cloudflare 中添加 A 记录:
   ```
   *.azure.a1d.ai → <静态IP>
   ```

## 📚 详细文档

- [Azure 快速开始](./docs/azure-quickstart.md) - 5分钟快速部署指南
- [Azure 认证设置](./docs/azure-auth-setup.md) - 详细的认证配置
- [Azure 部署指南](./docs/azure-deployment-guide.md) - 完整部署流程
- [Cloudflare 证书设置](./docs/cloudflare-origin-certificate-setup.md) - 证书配置详解

## 🔍 故障排除

### 常见问题

1. **认证失败**: 确保 `az login` 成功且有正确的订阅权限
2. **证书错误**: 检查 PFX 文件格式和密码是否正确
3. **网络问题**: 验证子网 CIDR 不与现有网络冲突
4. **权限不足**: 确保 Azure 账户有创建资源的权限

### 调试命令

```bash
# 检查 Azure 认证状态
az account show

# 验证 Pulumi 配置
pulumi config

# 查看详细日志
pulumi up --logtostderr -v=9
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📄 许可证

本项目采用 MIT 许可证。
