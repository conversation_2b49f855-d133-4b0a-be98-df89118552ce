/**
 * Example: Azure Infrastructure with Database Services
 *
 * This example shows how to use the modular structure to deploy
 * Container Apps infrastructure with PostgreSQL and Redis.
 */

import * as pulumi from "@pulumi/pulumi";
import * as azure from "@pulumi/azure-native";
import { authorization } from "@pulumi/azure-native";

// Import database modules
import {
    createPostgreSQL,
    createRedis,
    createDatabaseNSGs,
    ModuleConfig,
    DatabaseConfig
} from "../modules/database";

// Import storage modules (optional)
// import { createStorage, createCDN } from "../modules/storage";

// Get configuration
const config = new pulumi.Config();
const projectName = config.get("projectName") || "a1d-azure";
const location = config.get("location") || "East Asia";
const dbPassword = config.requireSecret("dbPassword");

// Common tags
const commonTags = {
    Project: projectName,
    Environment: pulumi.getStack(),
    CreatedBy: "<PERSON>ulum<PERSON>",
    CostCenter: projectName,
};

// Create Resource Group
const resourceGroup = new azure.resources.ResourceGroup(`${projectName}-rg`, {
    location: location,
    tags: commonTags,
});

// Create VNet and subnets (simplified for example)
const vnet = new azure.network.VirtualNetwork(`${projectName}-vnet`, {
    resourceGroupName: resourceGroup.name,
    location: resourceGroup.location,
    addressSpace: { addressPrefixes: ["10.0.0.0/16"] },
    tags: commonTags,
});

const privateSubnet = new azure.network.Subnet(`${projectName}-private-subnet`, {
    resourceGroupName: resourceGroup.name,
    virtualNetworkName: vnet.name,
    addressPrefix: "********/24",
    subnetName: "private",
});

// 🎯 Configure modules
const moduleConfig: ModuleConfig = {
    projectName,
    resourceGroupName: resourceGroup.name,
    location,
    privateSubnetId: privateSubnet.id,
    commonTags,
};

const databaseConfig: DatabaseConfig = {
    ...moduleConfig,
    dbPassword,
};

// 🎯 Deploy database services using modules
const nsgs = createDatabaseNSGs(moduleConfig);
const postgres = createPostgreSQL(databaseConfig);
const redis = createRedis(moduleConfig);

// 🎯 Optional: Deploy storage services
// const storage = createStorage(moduleConfig);
// const cdn = createCDN(moduleConfig, storage.storageAccount);

// Export database connection info
export const postgresConnectionString = postgres.outputs.connectionString;
export const redisConnectionString = redis.outputs.connectionString;
export const databaseOutputs = {
    postgres: postgres.outputs,
    redis: redis.outputs,
};

// Export NSG IDs
export const networkSecurityGroups = nsgs.outputs;