/**
 * ===================================================================
 * Azure Container Apps Infrastructure with Cloudflare Integration
 * ===================================================================
 *
 * 🚀 Prerequisites:
 * 1. Azure CLI configured: `az login`
 * 2. Pulumi Azure Native provider configured
 * 3. Cloudflare Origin Certificate (PFX format) ready
 *
 * 📋 Required Configuration:
 * ```bash
 * # Essential settings
 * pulumi config set azure-native:location "East Asia"
 *
 * # Cloudflare Origin Certificate (get from Cloudflare Dashboard -> SSL/TLS -> Origin Server)
 * pulumi config set --secret originPfxBase64 "<base64-encoded-pfx-file>"
 * pulumi config set --secret originPfxPassword "<pfx-password>"
 * ```
 *
 * 🎯 This creates:
 * - VNet with public subnet for Container Apps
 * - Container Apps Environment with static IP and custom DNS suffix
 * - Wildcard certificate (*.whiteboardanimation.ai) for HTTPS
 * - Container Registry for Docker images
 * - Key Vault for secure storage
 * - Log Analytics for monitoring
 *
 * 📊 Outputs:
 * - containerAppsEnvironmentStaticIp: Use for DNS A records
 * - wildcardCertificateId: Reference in Container Apps
 */

import * as pulumi from "@pulumi/pulumi";
import * as azure from "@pulumi/azure-native";
import { authorization } from "@pulumi/azure-native";

// 🎯 Optional modules - uncomment to enable additional services
// import * from "./modules"; // Enable all optional modules
// import { createPostgreSQL, createRedis } from "./modules/database";
// import { createStorage, createCDN } from "./modules/storage";

// Get configuration from Pulumi config
const config = new pulumi.Config();
const projectName = config.get("projectName") || "a1d-azure";
const location = config.get("location") || "East Asia";

// Common tags for all resources
const commonTags = {
	Project: projectName,
	Environment: pulumi.getStack(),
	CreatedBy: "Pulumi",
	CostCenter: projectName,
};

// ===================================================================================
// 🏗️ Core Infrastructure
// ===================================================================================

// Create Resource Group - Azure 中所有资源的容器
const resourceGroup = new azure.resources.ResourceGroup(`${projectName}-rg`, {
	location: location,
	tags: commonTags,
});

// Create Virtual Network (equivalent to AWS VPC)
const vnet = new azure.network.VirtualNetwork(`${projectName}-vnet`, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	addressSpace: {
		addressPrefixes: ["10.0.0.0/16"],
	},
	tags: commonTags,
});

// Create public subnet (equivalent to AWS public subnet)
const publicSubnet = new azure.network.Subnet(`${projectName}-public-subnet`, {
	resourceGroupName: resourceGroup.name,
	virtualNetworkName: vnet.name,
	addressPrefix: "10.0.0.0/23",
	subnetName: "public",
});

// Create private subnet (equivalent to AWS private subnet)
const privateSubnet = new azure.network.Subnet(
	`${projectName}-private-subnet`,
	{
		resourceGroupName: resourceGroup.name,
		virtualNetworkName: vnet.name,
		addressPrefix: "********/24",
		subnetName: "private",
	}
);

// Create Network Security Group for Container Apps
const containerAppsNsg = new azure.network.NetworkSecurityGroup(
	`${projectName}-containerapp-nsg`,
	{
		resourceGroupName: resourceGroup.name,
		location: resourceGroup.location,
		securityRules: [
			{
				name: "AllowHTTP",
				protocol: azure.network.SecurityRuleProtocol.Tcp,
				sourcePortRange: "*",
				destinationPortRange: "80",
				sourceAddressPrefix: "*",
				destinationAddressPrefix: "*",
				access: azure.network.SecurityRuleAccess.Allow,
				priority: 1000,
				direction: azure.network.SecurityRuleDirection.Inbound,
			},
			{
				name: "AllowHTTPS",
				protocol: azure.network.SecurityRuleProtocol.Tcp,
				sourcePortRange: "*",
				destinationPortRange: "443",
				sourceAddressPrefix: "*",
				destinationAddressPrefix: "*",
				access: azure.network.SecurityRuleAccess.Allow,
				priority: 1001,
				direction: azure.network.SecurityRuleDirection.Inbound,
			},
			{
				name: "AllowCustomPorts",
				protocol: azure.network.SecurityRuleProtocol.Tcp,
				sourcePortRange: "*",
				destinationPortRange: "3000-8080",
				sourceAddressPrefix: "*",
				destinationAddressPrefix: "*",
				access: azure.network.SecurityRuleAccess.Allow,
				priority: 1002,
				direction: azure.network.SecurityRuleDirection.Inbound,
			},
			{
				name: "AllowOutbound",
				protocol: azure.network.SecurityRuleProtocol.Tcp,
				sourcePortRange: "*",
				destinationPortRange: "*",
				sourceAddressPrefix: "*",
				destinationAddressPrefix: "*",
				access: azure.network.SecurityRuleAccess.Allow,
				priority: 1000,
				direction: azure.network.SecurityRuleDirection.Outbound,
			},
		],
		tags: commonTags,
	}
);

// ===================================================================================
// 📊 Monitoring and Logging
// ===================================================================================

// Create Log Analytics Workspace for Container Apps logging
const logAnalyticsWorkspace = new azure.operationalinsights.Workspace(
	`${projectName}-logs`,
	{
		resourceGroupName: resourceGroup.name,
		location: resourceGroup.location,
		workspaceName: `${projectName}-logs`,
		sku: {
			name: azure.operationalinsights.WorkspaceSkuNameEnum.PerGB2018,
		},
		retentionInDays: 30,
		tags: commonTags,
	}
);

// ===================================================================================
// 🔐 Security and Certificates
// ===================================================================================

// Cloudflare Origin CA Wildcard Certificate configuration
const originPfxBase64 = config.requireSecret("originPfxBase64");
const originPfxPassword = config.requireSecret("originPfxPassword");

// Create Key Vault for secrets management
const keyVault = new azure.keyvault.Vault(`${projectName}-kv`, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	properties: {
		sku: {
			family: azure.keyvault.SkuFamily.A,
			name: azure.keyvault.SkuName.Standard,
		},
		tenantId: authorization.getClientConfig().then((config) => config.tenantId),
		accessPolicies: [],
		enableRbacAuthorization: true,
		enableSoftDelete: true,
		softDeleteRetentionInDays: 7,
		enablePurgeProtection: true,
	},
	tags: commonTags,
});

// Store certificate PFX in Key Vault
const wildcardCertificateSecret = new azure.keyvault.Secret(
	"wildcard-certificate",
	{
		vaultName: keyVault.name,
		resourceGroupName: resourceGroup.name,
		secretName: "wildcard-whiteboardanimation-ai-certificate",
		properties: {
			value: originPfxBase64,
			contentType: "application/x-pkcs12",
			attributes: { enabled: true },
		},
		tags: commonTags,
	}
);

// ===================================================================================
// 🐳 Container Infrastructure
// ===================================================================================

// Create Container Apps Environment with custom DNS suffix
const containerAppsEnvironment = new azure.app.ManagedEnvironment(
	`${projectName}-containerapp-env`,
	{
		resourceGroupName: resourceGroup.name,
		location: resourceGroup.location,
		environmentName: `${projectName}-containerapp-env`,
		vnetConfiguration: {
			infrastructureSubnetId: publicSubnet.id,
		},
		appLogsConfiguration: {
			destination: "log-analytics",
			logAnalyticsConfiguration: {
				customerId: logAnalyticsWorkspace.customerId,
				sharedKey: azure.operationalinsights
					.getSharedKeysOutput({
						resourceGroupName: resourceGroup.name,
						workspaceName: logAnalyticsWorkspace.name,
					})
					.primarySharedKey?.apply((key) => key ?? ""),
			},
		},
		// Environment-level Custom DNS Suffix configuration
		// This allows all apps to automatically use <app-name>.whiteboardanimation.ai domains
		customDomainConfiguration: {
			dnsSuffix: "whiteboardanimation.ai",
			certificateValue: originPfxBase64,
			certificatePassword: originPfxPassword,
		},
		tags: commonTags,
	},
	{ dependsOn: [publicSubnet, logAnalyticsWorkspace] }
);

// Register certificate with Container Apps Environment
const wildcardCertificate = new azure.app.Certificate(
	`${projectName}-wildcard-cert`,
	{
		resourceGroupName: resourceGroup.name,
		environmentName: containerAppsEnvironment.name,
		properties: {
			value: originPfxBase64,
			password: originPfxPassword,
		},
		tags: commonTags,
	}
);

// Create Azure Container Registry (equivalent to AWS ECR)
const sanitizedProjectName = projectName
	.toLowerCase()
	.replace(/[^a-z0-9]/g, "");
const acrRegistryName = sanitizedProjectName.substring(0, 47) + "acr";
const containerRegistry = new azure.containerregistry.Registry(
	acrRegistryName,
	{
		resourceGroupName: resourceGroup.name,
		location: resourceGroup.location,
		registryName: acrRegistryName,
		sku: {
			name: azure.containerregistry.SkuName.Basic,
		},
		adminUserEnabled: true,
		tags: commonTags,
	}
);

// ===================================================================================
// 📤 Exports
// ===================================================================================

// Core Infrastructure
export const resourceGroupName = resourceGroup.name;
export const resourceGroupLocation = resourceGroup.location;
export const vnetId = vnet.id;
export const vnetName = vnet.name;
export const publicSubnetId = publicSubnet.id;
export const privateSubnetId = privateSubnet.id;
export const vnetCidrBlock = "10.0.0.0/16";

// Container Infrastructure
export const containerAppsEnvironmentId = containerAppsEnvironment.id;
export const containerAppsEnvironmentName = containerAppsEnvironment.name;
export const containerAppsEnvironmentStaticIp =
	containerAppsEnvironment.staticIp;
export const logAnalyticsWorkspaceId = logAnalyticsWorkspace.id;
export const containerAppsNsgId = containerAppsNsg.id;

// Container Registry
export const acrName = containerRegistry.name;
export const acrLoginServer = containerRegistry.loginServer;

// Certificate and Security
export const wildcardCertificateId = wildcardCertificate.id;
export const wildcardCertificateName = wildcardCertificate.name;
export const wildcardDomain = "*.whiteboardanimation.ai";
export const keyVaultName = keyVault.name;
export const keyVaultUri = keyVault.properties.apply(
	(props) => props?.vaultUri
);

// Certificate Information
export const certificateInfo = {
	certificateId: wildcardCertificate.id,
	certificateName: wildcardCertificate.name,
	domain: "*.whiteboardanimation.ai",
	provider: "Cloudflare Origin CA (15 years)",
	keyVaultSecret: wildcardCertificateSecret.name,
	usage:
		"Reference certificateId when creating Container Apps with custom domains",
};

// Environment Configuration for Applications
export const envConfig = pulumi.jsonStringify({
	PROJECT_NAME: projectName,
	PLATFORM: "azure",
	ACR_LOGIN_SERVER: containerRegistry.loginServer,
});

// Azure-specific Configuration
export const azureConfig = pulumi.jsonStringify({
	RESOURCE_GROUP_NAME: resourceGroup.name,
	LOCATION: resourceGroup.location,
	VNET_ID: vnet.id,
	PUBLIC_SUBNET_ID: publicSubnet.id,
	PRIVATE_SUBNET_ID: privateSubnet.id,
	CONTAINER_APPS_NSG_ID: containerAppsNsg.id,
	ACR_NAME: containerRegistry.name,
	ACR_LOGIN_SERVER: containerRegistry.loginServer,
	CONTAINER_APPS_ENVIRONMENT_ID: containerAppsEnvironment.id,
	CONTAINER_APPS_ENVIRONMENT_NAME: containerAppsEnvironment.name,
	LOG_ANALYTICS_WORKSPACE_ID: logAnalyticsWorkspace.id,
});
