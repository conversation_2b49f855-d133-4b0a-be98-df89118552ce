import * as pulumi from "@pulumi/pulumi";
import * as azure from "@pulumi/azure-native";
import { ModuleConfig } from "./database";

// Create Storage Account and related resources
export function createStorage(config: ModuleConfig) {
    // Azure storage account names: 3-24 chars, lowercase letters and numbers only
    const sanitizedProjectName = config.projectName
        .toLowerCase()
        .replace(/[^a-z0-9]/g, "");

    const storageAccountName = sanitizedProjectName.substring(0, 16) + 'storage'; // Max 24 chars

    const storageAccount = new azure.storage.StorageAccount(storageAccountName, {
        resourceGroupName: config.resourceGroupName,
        location: config.location,
        sku: {
            name: azure.storage.SkuName.Standard_LRS,
        },
        kind: azure.storage.Kind.StorageV2,
        accessTier: azure.storage.AccessTier.Hot,
        allowBlobPublicAccess: true,
        tags: config.commonTags,
    });

    // Create a container for static files (equivalent to S3 bucket)
    const staticContainer = new azure.storage.BlobContainer(`${config.projectName}-static`, {
        resourceGroupName: config.resourceGroupName,
        accountName: storageAccount.name,
        containerName: "static",
        publicAccess: azure.storage.PublicAccess.Blob,
    });

    // Get storage account keys for connection string
    const storageKeys = azure.storage.listStorageAccountKeysOutput({
        resourceGroupName: config.resourceGroupName,
        accountName: storageAccount.name,
    });

    const storageConnectionString = pulumi.interpolate`DefaultEndpointsProtocol=https;AccountName=${storageAccount.name};AccountKey=${storageKeys.keys[0].value};EndpointSuffix=core.windows.net`;

    return {
        storageAccount,
        staticContainer,
        outputs: {
            storageAccountName: storageAccount.name,
            storageAccountPrimaryEndpoint: storageAccount.primaryEndpoints.apply(e => e.blob),
            staticContainerName: staticContainer.name,
            storageConnectionString,
        }
    };
}

// Create CDN Profile and Endpoint
export function createCDN(config: ModuleConfig, storageAccount: azure.storage.StorageAccount) {
    const cdnProfile = new azure.cdn.Profile(`${config.projectName}-cdn-profile`, {
        resourceGroupName: config.resourceGroupName,
        location: "Global", // CDN profiles are global
        profileName: `${config.projectName}-cdn-profile`,
        sku: {
            name: azure.cdn.SkuName.Standard_Microsoft,
        },
        tags: config.commonTags,
    });

    const cdnEndpoint = new azure.cdn.Endpoint(`${config.projectName}-cdn-endpoint`, {
        resourceGroupName: config.resourceGroupName,
        profileName: cdnProfile.name,
        endpointName: `${config.projectName}-cdn`,
        location: "Global",
        origins: [{
            name: "storage-origin",
            hostName: storageAccount.primaryEndpoints.apply(endpoints =>
                endpoints.blob?.replace("https://", "").replace("/", "") || ""
            ),
            httpPort: 80,
            httpsPort: 443,
        }],
        isHttpAllowed: false,
        isHttpsAllowed: true,
        originHostHeader: storageAccount.primaryEndpoints.apply(endpoints =>
            endpoints.blob?.replace("https://", "").replace("/", "") || ""
        ),
        tags: config.commonTags,
    });

    return {
        cdnProfile,
        cdnEndpoint,
        outputs: {
            cdnEndpointHostname: cdnEndpoint.hostName,
            cdnUrl: pulumi.interpolate`https://${cdnEndpoint.hostName}`,
            cdnProfileName: cdnProfile.name,
            cdnEndpointName: cdnEndpoint.name,
        }
    };
}