import * as pulumi from "@pulumi/pulumi";
import * as azure from "@pulumi/azure-native";

/**
 * ===================================================================
 * Database Module Configuration Guide
 * ===================================================================
 *
 * 🎯 Usage Example:
 *
 * ```typescript
 * import { createPostgreSQL, DatabaseConfig } from "./modules/database";
 *
 * const config: DatabaseConfig = {
 *   projectName: "myproject",
 *   resourceGroupName: resourceGroup.name,
 *   location: "East Asia",
 *   privateSubnetId: privateSubnet.id,
 *   commonTags: { Project: "MyProject" },
 *   dbPassword: config.requireSecret("dbPassword"),
 *   dbName: "productiondb",           // Optional: defaults to "maindb"
 *   dbCollation: "zh_CN.utf8"         // Optional: defaults to "C.utf8"
 * };
 *
 * const postgres = createPostgreSQL(config);
 * ```
 *
 * 🌐 Collation Options for Multi-language Support:
 * - "C.utf8"       - Recommended default (best Unicode support)
 * - "zh_CN.utf8"   - Chinese Simplified
 * - "zh_TW.utf8"   - Chinese Traditional
 * - "en_US.utf8"   - English US (original default)
 * - "ja_JP.utf8"   - Japanese
 * - "ko_KR.utf8"   - Korean
 *
 * ℹ️  Note: Collation affects text sorting/comparison, not storage.
 *    UTF8 encoding supports all Unicode characters including Chinese.
 */

// Common interface for all modules
export type ModuleConfig = {
	projectName: string;
	resourceGroupName: pulumi.Output<string>;
	location: string;
	privateSubnetId: pulumi.Output<string>;
	commonTags: Record<string, string>;
};

// Database module configuration
export type DatabaseConfig = ModuleConfig & {
	dbPassword: pulumi.Output<string>;
	dbName?: string; // Optional database name, defaults to "maindb"
	dbCollation?: string; // Optional collation, defaults to "C.utf8" for better Unicode support
};

// Create PostgreSQL Flexible Server
export function createPostgreSQL(config: DatabaseConfig) {
	const dbName = config.dbName || "maindb";
	const dbCollation = config.dbCollation || "C.utf8"; // C.utf8 provides better Unicode support for international text

	const postgresServer = new azure.dbforpostgresql.Server(
		`${config.projectName}-postgres`,
		{
			resourceGroupName: config.resourceGroupName,
			location: config.location,
			serverName: `${config.projectName}-postgres`,
			sku: {
				name: "Standard_B1ms", // ARM-based instance for cost optimization
				tier: azure.dbforpostgresql.SkuTier.Burstable,
			},
			storage: {
				storageSizeGB: 32,
			},
			version: azure.dbforpostgresql.ServerVersion.ServerVersion_16,
			administratorLogin: "postgres",
			administratorLoginPassword: config.dbPassword,
			network: {
				delegatedSubnetResourceId: config.privateSubnetId,
			},
			tags: config.commonTags,
		}
	);

	// Create database
	const database = new azure.dbforpostgresql.Database(
		`${config.projectName}-${dbName}`,
		{
			resourceGroupName: config.resourceGroupName,
			serverName: postgresServer.name,
			databaseName: dbName,
			charset: "UTF8",
			collation: dbCollation,
		}
	);

	return {
		server: postgresServer,
		database: database,
		outputs: {
			serverName: postgresServer.name,
			serverFqdn: postgresServer.fullyQualifiedDomainName,
			port: 5432,
			databaseName: database.name,
			connectionString: pulumi.interpolate`postgresql://postgres:${config.dbPassword}@${postgresServer.fullyQualifiedDomainName}:5432/${dbName}?sslmode=require`,
		},
	};
}

// Create Redis Cache
export function createRedis(config: ModuleConfig) {
	const redisCache = new azure.redis.Redis(`${config.projectName}-redis`, {
		resourceGroupName: config.resourceGroupName,
		location: config.location,
		sku: {
			name: azure.redis.SkuName.Premium,
			family: azure.redis.SkuFamily.P,
			capacity: 1, // P1 = Premium 1 GB
		},
		enableNonSslPort: false,
		minimumTlsVersion: azure.redis.TlsVersion.TlsVersion_1_2,
		redisConfiguration: {
			maxmemoryPolicy: "allkeys-lru",
		},
		subnetId: config.privateSubnetId,
		tags: config.commonTags,
	});

	// Get Redis primary key
	const redisPrimaryKey = azure.redis.listRedisKeysOutput({
		name: redisCache.name,
		resourceGroupName: config.resourceGroupName,
	}).primaryKey;

	return {
		cache: redisCache,
		outputs: {
			name: redisCache.name,
			hostname: redisCache.hostName,
			port: redisCache.port,
			sslPort: redisCache.sslPort,
			primaryKey: redisPrimaryKey,
			connectionString: pulumi.interpolate`rediss://:${redisPrimaryKey}@${redisCache.hostName}:${redisCache.sslPort}`,
		},
	};
}

// Create Network Security Groups for database services
export function createDatabaseNSGs(config: ModuleConfig) {
	// Network Security Group for database
	const dbNsg = new azure.network.NetworkSecurityGroup(
		`${config.projectName}-db-nsg`,
		{
			resourceGroupName: config.resourceGroupName,
			location: config.location,
			securityRules: [
				{
					name: "AllowPostgreSQL",
					protocol: azure.network.SecurityRuleProtocol.Tcp,
					sourcePortRange: "*",
					destinationPortRange: "5432",
					sourceAddressPrefix: "10.0.0.0/16",
					destinationAddressPrefix: "*",
					access: azure.network.SecurityRuleAccess.Allow,
					priority: 1000,
					direction: azure.network.SecurityRuleDirection.Inbound,
				},
			],
			tags: config.commonTags,
		}
	);

	// Network Security Group for Redis
	const redisNsg = new azure.network.NetworkSecurityGroup(
		`${config.projectName}-redis-nsg`,
		{
			resourceGroupName: config.resourceGroupName,
			location: config.location,
			securityRules: [
				{
					name: "AllowRedis",
					protocol: azure.network.SecurityRuleProtocol.Tcp,
					sourcePortRange: "*",
					destinationPortRange: "6379",
					sourceAddressPrefix: "10.0.0.0/16",
					destinationAddressPrefix: "*",
					access: azure.network.SecurityRuleAccess.Allow,
					priority: 1000,
					direction: azure.network.SecurityRuleDirection.Inbound,
				},
			],
			tags: config.commonTags,
		}
	);

	return {
		dbNsg,
		redisNsg,
		outputs: {
			dbNsgId: dbNsg.id,
			redisNsgId: redisNsg.id,
		},
	};
}
