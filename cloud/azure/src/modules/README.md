# Azure Infrastructure Modules

## 🎯 概述

这个模块化结构让您可以选择性地部署不同的Azure服务组件，而不是一次部署所有内容。

## 📁 模块结构

```
modules/
├── index.ts          # 模块导出控制
├── database.ts       # PostgreSQL + Redis
├── storage.ts        # Storage Account + CDN
└── examples/
    └── with-database.ts
```

## 🔧 使用方法

### 方式一：在主 index.ts 中启用模块

```typescript
// 在 cloud/azure/src/index.ts 中取消注释：

// 启用所有模块
import * from "./modules";

// 或选择性启用
import { createPostgreSQL, createRedis } from "./modules/database";
import { createStorage } from "./modules/storage";

// 然后在代码中使用
const postgres = createPostgreSQL(databaseConfig);
const redis = createRedis(moduleConfig);
```

### 方式二：注释控制

在 `modules/index.ts` 中注释掉不需要的模块：

```typescript
// 启用数据库模块
export * from "./database";

// 禁用存储模块
// export * from "./storage";
```

### 方式三：独立使用

创建新的入口文件（如 `examples/with-database.ts`）：

```typescript
import { createPostgreSQL } from "../modules/database";

// 只部署需要的服务
const postgres = createPostgreSQL(config);
```

## 📦 可用模块

### Database Module (`database.ts`)

包含：
- PostgreSQL Flexible Server
- Redis Cache (Premium)
- Database NSGs

**函数**：
- `createPostgreSQL(config: DatabaseConfig)`
- `createRedis(config: ModuleConfig)`
- `createDatabaseNSGs(config: ModuleConfig)`

### Storage Module (`storage.ts`)

包含：
- Storage Account
- Blob Container
- CDN Profile & Endpoint

**函数**：
- `createStorage(config: ModuleConfig)`
- `createCDN(config: ModuleConfig, storageAccount)`

## ⚙️ 配置类型

```typescript
type ModuleConfig = {
    projectName: string;
    resourceGroupName: pulumi.Output<string>;
    location: string;
    privateSubnetId: pulumi.Output<string>;
    commonTags: Record<string, string>;
};

type DatabaseConfig = ModuleConfig & {
    dbPassword: pulumi.Output<string>;
};
```

## 🎯 使用场景

### 基础版（当前默认）
- 只有Container Apps + 证书
- 不包含数据库、存储等额外服务
- 适合简单的容器化应用

### 数据库版
```typescript
// 取消注释
import { createPostgreSQL, createRedis } from "./modules/database";
```

### 全功能版
```typescript
// 取消注释
import * from "./modules";
```

## 💡 优势

1. **按需部署**：只部署需要的服务
2. **成本控制**：避免不必要的资源费用
3. **代码清晰**：主文件保持简洁
4. **易于维护**：模块化管理
5. **灵活配置**：注释即可禁用

## 🔄 迁移现有项目

如果您已有项目使用注释版本的 index.ts：

1. 备份当前 `index.ts`
2. 使用新的 `index-clean.ts` 作为基础
3. 根据需要启用相应模块
4. 测试部署确保一切正常

## 📝 示例

查看 `examples/with-database.ts` 了解完整的使用示例。